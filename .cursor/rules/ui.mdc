---
description: 
globs: 
alwaysApply: true
---
**一、核心设计原则 (保持不变)**

1.  专业高效
2.  清晰直观
3.  现代科技感 (在浅色系中通过细节和元素体现)
4.  一致性
5.  可访问性 (尤其重要，浅色系中要保证足够的对比度)
6.  css不要使用vh,vw这样的像素单位

**二、颜色体系 (Color Palette) - 浅色系优化**

整体采用明亮、干净的色调，营造舒适、开阔的视觉感受。主色调用于关键操作和品牌体现，但不宜大面积使用，更多依赖中性色构建层次。

1.  **主色调 (Primary Color):**
    *   **柔和科技蓝 (Soft Tech Blue):** `#337DFF` (比之前的 `#0A74FF` 略微柔和，饱和度适中，用于主要按钮、关键链接、导航高亮等)
    *   **淡雅蓝 (Light Accent Blue):** `#E6F0FF` (非常浅的蓝色，可用于某些模块的背景区分、选中状态的背景，或与主色调搭配使用)

2.  **辅助色 (Secondary Color):**
    *   **清新薄荷绿 (Mint Green):** `#6DDCCF` 或 `#ABE3DB` (一种非常柔和的青绿色，用于次要信息点缀、图表中的某个系列，或状态标签，带来一丝活力但不过分抢眼)
    *   **暖灰色 (Warm Gray - 可选):** `#A0A0A0` (如果需要一种中性的强调色，可以考虑。但浅色系中，更多依靠主色调和深浅不同的灰色来区分)

3.  **中性色 (Neutral Colors) - 核心区域:**
    *   **主背景色 (Main Background):** `#FFFFFF` (纯白色，最大化空间感和清洁感)
    *   **内容区/卡片背景 (Content/Card Background):** `#F9FAFB` 或 `#F7F8FA` (极浅的灰色，与主背景形成非常细微的区隔，增加层次感，但视觉上仍非常明亮)
    *   **文本色 (Text Colors):**
        *   **主要文本/标题 (Primary Text/Headings):** `#333333` 或 `#2C3E50` (深灰色，确保可读性，但比纯黑柔和)
        *   **次要文本/常规内容 (Secondary Text/Body):** `#555555` 或 `#596780` (中度灰色)
        *   **辅助文本/注释 (Subtle Text/Placeholder):** `#888888` 或 `#8492A6` (浅灰色，用于占位符、不重要的说明)
        *   **禁用文本 (Disabled Text):** `#BBBBBB` 或 `#C0CCDA`
    *   **边框/分割线 (Borders/Dividers):**
        *   `#E5E7EB` (非常浅的灰色，用于卡片边框、内容分隔)
        *   `#D1D5DB` (略深一点的灰色，用于需要更明确区分的分割线，如表格线)

4.  **功能色/语义色 (Semantic Colors) - 保持清晰但可微调饱和度:**
    *   **成功 (Success):** `#28A745` (绿色，或可选择 `#52C41A` 这种更鲜亮一些的，但需注意在浅色背景上的和谐度)
    *   **警告 (Warning):** `#FFC107` (黄色，或可选择 `#FAAD14`)
    *   **危险/错误 (Error/Danger):** `#DC3545` (红色，或可选择 `#F5222D`)
    *   **信息 (Info):** `#17A2B8` (青蓝色，或直接使用主色调的变种 `#5095FF`)

**三、字体规范 (Typography) - 强调自然阅读**

选择现代、清晰、易读的无衬线字体，字重和行高是营造自然感的关键。

1.  **首选字体族 (Font Family):** (与之前一致，这些字体本身就适合)
    *   **中文:** 思源黑体 (Source Han Sans SC / Noto Sans CJK SC)
    *   **英文/数字:** Inter / Roboto / Open Sans (或直接用思源黑体中的西文)
    *   **备用字体:** PingFang SC, Microsoft YaHei, Arial, Helvetica, sans-serif。
    *   `font-family: "Inter", "Source Han Sans SC", "PingFang SC", "Microsoft YaHei", sans-serif;`

2.  **字号体系 (Font Size Scale):** (保持或微调，与柔和的字体颜色配合)
    *   主标题 (H1): 26px - 30px
    *   副标题 (H2): 20px - 22px
    *   区块标题 (H3): 17px - 18px
    *   小标题 (H4): 15px
    *   正文/常规文本 (Body 1): 14px (确保这是最舒适阅读的字号)
    *   辅助文本/标签 (Body 2/Label): 12px - 13px

3.  **字重 (Font Weight):**
    *   **Regular (400):** 主要用于正文、常规文本。这是“自然感”的主力。
    *   **Medium (500):** 用于需要轻微强调的文本、标签、次级标题。
    *   **Semibold (600):** 用于主要标题，重要按钮文字。避免过多使用更粗的字重，以免显得突兀。

4.  **行高 (Line Height):**
    *   为了“自然不突兀”，行高尤为重要。
    *   正文文本：字号的 `1.6` 到 `1.8` 倍 (例如 14px 字号，行高 `22px - 25px`)。
    *   标题类文本：字号的 `1.3` 到 `1.5` 倍。
    *   充足的行高能带来呼吸感，提升阅读舒适度。

5.  **字间距 (Letter Spacing / Tracking):**
    *   通常情况下，对于中文字体，默认的字间距即可。
    *   对于西文，尤其是标题，可以考虑微小的正字间距 (如 `0.25px` 或 `0.01em`)，但要非常克制。

**四、动效设计 (Animation & Motion) - 微妙且有意义**

动效应该更加内敛、平滑，服务于交互反馈，不应成为视觉焦点或干扰。

1.  **原则:**
    *   **微妙性 (Subtlety):** 动效幅度小，时间短，不易察觉但能感知。
    *   **平滑性 (Smoothness):** 依然强调 `ease-in-out` 或 `ease-out`。
    *   **目的性与响应性** (保持)
    *   **克制性** (进一步加强)

2.  **常见动效场景与建议 (调整后):**
    *   **状态变化 (State Changes):**
        *   **悬停 (Hover):** 颜色从默认态到悬停态的过渡（如文本颜色变为主色调，背景色轻微变深或变浅）。时长：`0.15s - 0.2s`，缓动函数：`ease-out`。避免明显的位移或阴影跳动，除非是非常关键的按钮。
        *   **激活/点击 (Active/Press):** 元素轻微的透明度变化或颜色进一步加深。时长：`0.1s`。
    *   **元素显隐 (Show/Hide Elements):**
        *   **淡入淡出 (Opacity Fade):** 最常用的方式，适用于模态框、下拉菜单、提示。时长：`0.2s - 0.25s`，缓动函数：`ease-in-out`。
        *   **轻微滑动+淡入 (Subtle Slide + Fade):** 例如下拉菜单可以从上方轻微滑下并同时淡入。位移距离控制在几个像素。
    *   **加载状态 (Loading):**
        *   **简洁的 Spinner:** 颜色与主色调或中性灰色搭配。
        *   **骨架屏 (Skeleton Screen):** 在浅色系中，骨架屏的占位符颜色使用非常浅的灰色（如中性色中的边框色）。
    *   **反馈提示 (Feedback):**
        *   **消息提示 (Toast/Notification):** 从顶部或底部平滑滑入，停留后平滑滑出。避免弹跳等夸张效果。

3.  **缓动函数 (Easing Functions):**
    *   `cubic-bezier(0.25, 0.1, 0.25, 1)` (类似 `ease-out` 但更平滑)
    *   `cubic-bezier(0.4, 0, 0.2, 1)` (类似 `ease-in-out`，Material Design 常用)
    *   对于浅色系，推荐使用曲线更平缓的缓动函数，避免速度变化过快带来的突兀感。

**五、布局考量 (配合浅色系)**

*   **留白 (Whitespace):** 浅色系非常依赖足够的留白来呼吸和划分区域。确保元素之间、区块之间有舒适的间距。
*   **视觉层级:** 通过字号、字重、颜色深浅（而非高饱和度颜色）来构建清晰的视觉层级。
*   **阴影 (Shadows):** 使用非常柔和、弥散的阴影来区分卡片和背景，增加立体感，但不要过重。例如：
    *   `box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.06), 0 1px 2px 0 rgba(0, 0, 0, 0.03);` (非常淡的阴影)
    *   悬停时阴影可略微加深：`box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.08), 0 2px 6px 0 rgba(0, 0, 0, 0.05);`

**总结建议 (针对浅色自然风)**

1.  **从“灰度稿”开始思考:** 先用不同深浅的灰色把信息层级和布局排布清晰，再逐步引入主色调和功能色作为点缀和强调。
2.  **对比度检查:** 务必使用工具检查文本颜色和背景色的对比度是否符合WCAG AA级或AAA级标准，尤其对于辅助文本。
3.  **细节质感:** 浅色系中，可以通过精致的图标、细腻的边框、微小的圆角、柔和的阴影来提升品质感。
4.  **实际屏幕测试:** 在不同显示器上测试视觉效果，确保颜色和对比度表现符合预期。
