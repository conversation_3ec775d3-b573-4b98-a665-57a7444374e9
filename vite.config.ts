/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-11-11
 * @LastEditors: 余承
 * @LastEditTime: 2025-06-18
 */
import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Unocss from 'unocss/vite'
import { presetUno, presetAttributify } from 'unocss'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { resolve } from 'node:path'
const pathSrc = resolve(__dirname, 'src')

export default defineConfig({
  base: './',
  plugins: [
    vue(),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [resolve(pathSrc, 'assets/svgs')],
      // 指定symbolId格式
      symbolId: 'icon-[dir]-[name]',
    }),
    Unocss({
      configFile: './uno.config.ts',
      presets: [presetUno(), presetAttributify()],
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ['legacy-js-api'],
        additionalData: '@use "@/style/mixin.scss" as *;',
      },
    },
  },
  server: {
    host: true,
    port: 9988,
    proxy: {
      '/iiot': {
        target: 'https://public.szigc.com:9000/api/iiot/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/iiot/, ''),
      },
    },
  },
})
