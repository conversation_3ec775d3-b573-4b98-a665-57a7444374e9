/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-11-11
 * @LastEditors: 余承
 * @LastEditTime: 2025-05-21
 */
import skipFormatting from "@vue/eslint-config-prettier/skip-formatting";
import vueTsEslintConfig from "@vue/eslint-config-typescript";
import oxlint from "eslint-plugin-oxlint";
import pluginVue from "eslint-plugin-vue";

export default [
  {
    name: "app/files-to-lint",
    files: ["**/*.{ts,mts,tsx,vue}"],
  },

  {
    name: "app/files-to-ignore",
    ignores: ["**/dist/**", "**/dist-ssr/**", "**/coverage/**"],
  },

  ...pluginVue.configs["flat/essential"],
  ...vueTsEslintConfig(),
  oxlint.configs["flat/recommended"],
  skipFormatting,
  {
    "vue/multi-word-component-names": [
      "error",
      {
        ignores: [],
      },
    ],
  },
];
