{"version": 1, "include": ["**/*.{vue,ts,js,tsx,jsx}"], "exclude": ["**/node_modules/**", "**/dist/**", "**/build/**"], "rules": {"vue-component-structure": {"pattern": "src/components/**/*.vue", "structure": {"template": {}, "script": {"setup": true, "lang": "ts"}, "style": {"scoped": true, "lang": "scss"}}}, "view-structure": {"pattern": "src/views/**/*.vue", "structure": {"template": {}, "script": {"setup": true, "lang": "ts"}, "style": {"scoped": true, "lang": "scss"}}}, "store-pattern": {"pattern": "src/stores/**/*.ts", "template": "import { defineStore } from 'pinia'\n\nexport const use{name}Store = defineStore('{name}', {\n  state: () => ({\n    // state\n  }),\n  getters: {\n    // getters\n  },\n  actions: {\n    // actions\n  }\n})"}, "composable-pattern": {"pattern": "src/hooks/use*.ts", "template": "import { ref } from 'vue'\n\nexport function {name}() {\n  // composable logic\n  return {\n    // return values\n  }\n}"}, "utils-pattern": {"pattern": "src/utils/**/*.ts", "template": "// Utility function for {name}\n\nexport function {name}() {\n  // implementation\n}"}}, "formatting": {"indentSize": 2, "useTabs": false, "singleQuote": true, "trailingComma": "none", "semi": false, "printWidth": 100}, "imports": {"groups": [{"name": "vue", "match": ["vue", "vue-router", "pinia", "@vueuse/core"]}, {"name": "components", "match": ["@/components/**"]}, {"name": "composables", "match": ["@/hooks/**"]}, {"name": "stores", "match": ["@/stores/**"]}, {"name": "utils", "match": ["@/utils/**"]}, {"name": "types", "match": ["@/types/**"]}, {"name": "styles", "match": ["@/style/**"]}]}}