{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "node_modules/vue-cesium/Cesium.d.ts", "node_modules/vue-cesium/global.d.ts", "src/types/*.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "types": ["vue-cesium/global", "vue-cesium/Cesium"], "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}