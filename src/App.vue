<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-05-21
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-12
-->
<template>
  <n-config-provider w-full h-full :theme-overrides="themeOverrides" :date-locale="dateZhCN" :locale="zhCN">
    <n-message-provider>
      <v-scale-screen :width="2560" :height="1440" :delay="200">
        <router-view></router-view>
      </v-scale-screen>
    </n-message-provider>
  </n-config-provider>
</template>

<script lang="ts" setup>
import { NMessageProvider, NConfigProvider, dateZhCN, zhCN } from 'naive-ui'
import themeOverrides from '@/config/themeOverrides.json'
import VScaleScreen from 'v-scale-screen'
import { useRouter } from 'vue-router'
document.addEventListener('contextmenu', (e) => {
  e.preventDefault()
})

const router = useRouter()

router.replace('/')
</script>

<style lang="scss">
#app {
  width: 100vw;
  height: 100vh;
  font-size: 16px;
  overflow: hidden;
  color: #fff;
  background-color: #c0c0c0;
}
</style>
