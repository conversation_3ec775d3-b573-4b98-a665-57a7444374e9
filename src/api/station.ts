import { get } from '@/utils/http'

/**
 * @description 园区数据
 * @returns
 */
export const getStationInfoApi = () => get('/station/getstationinfo')

/**
 * @description 值班信息
 * @returns
 */
export const getDutyInfoRmationApi = () => get('/station/getdutyinformations')

/**
 * @description 功勋荣誉
 * @returns
 */
export const getHonorsApi = () => get('/station/gethonors')

/**
 * @description 环境信息
 * @returns
 */
export const getEnvironmentInfoApi = () => get('/station/getenvironmentinfo')

/**
 * @description 上下行播出数据
 * @returns
 */
export const getBroadcastDataApi = () => get('/station/getbroadcastdatas')

/**
 * @description 播出时间
 * @returns
 */
export const getBroadcastTimeApi = () => get('/station/getbroadcasttime')

/**
 * @description 重置播出时间
 * @returns
 */
export const resetBroadcastTimeApi = () => get('/station/resetbroadcasttime')

/**
 * @description 雨雪状态
 * @returns
 */
export const getWeatherStatusApi = () => get('/station/getweatherstatus')

/**
 * @description 提醒分类统计
 * @returns
 */
export const getStatsRemindTypesApi = () => get('/station/statsremindtypes')

/**
 * @description 提醒列表
 * @returns
 */
export const getActiveRemainsApi = () => get('/station/getactiveremains')
