/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-11-11
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-10
 */
import 'virtual:uno.css'
import 'virtual:svg-icons-register'
import '@unocss/reset/normalize.css'
import '@/style/global.scss'
import 'default-passive-events'
import '@/assets/iconfont/iconfont.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'

const app = createApp(App)

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

app.use(pinia)
app.use(router)

app.mount('#app')
