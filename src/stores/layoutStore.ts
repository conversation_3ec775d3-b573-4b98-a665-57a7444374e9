/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-20
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-11
 */
import { defineStore } from 'pinia'

export const useLayoutStore = defineStore('layout', {
  state: () => ({
    showLeftPanel: true,
    showRightPanel: true,
    showBottomMenu: true,
    showTop: true,
    dataCenterRoom: '',
  }),
  actions: {
    setShowLeftPanel(show: boolean) {
      this.showLeftPanel = show
    },
    setShowRightPanel(show: boolean) {
      this.showRightPanel = show
    },
    setShowBottomMenu(show: boolean) {
      this.showBottomMenu = show
    },
    setShowTop(show: boolean) {
      this.showTop = show
      this.setShowBottomMenu(show)
      this.setShowRightPanel(show)
      this.setShowLeftPanel(show)
    },
    setDataCenterRoom(room: string) {
      this.dataCenterRoom = room
    },
  },
  persist: true,
})
