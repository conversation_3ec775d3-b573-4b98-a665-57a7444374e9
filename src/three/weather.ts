import * as THREE from 'three'

export class WeatherSystem {
  private scene: THREE.Scene
  private camera: THREE.Camera
  private cloudParticles: THREE.Points[] = []
  private raindrops: THREE.Points[] = []
  private flash: THREE.PointLight
  private ambient: THREE.AmbientLight
  private isRaining: boolean = false

  constructor(scene: THREE.Scene, camera: THREE.Camera) {
    this.scene = scene
    this.camera = camera

    // 设置环境光
    this.ambient = new THREE.AmbientLight(0x555555)
    this.scene.add(this.ambient)

    // 设置闪电光源
    this.flash = new THREE.PointLight(0x062d89, 30, 500, 1.7)
    this.flash.position.set(200, 300, 100)
    this.scene.add(this.flash)
  }

  // 创建云层
  async createClouds() {
    // 加载云朵纹理
    const textureLoader = new THREE.TextureLoader()
    const cloudTexture = await textureLoader.loadAsync('/textures/smoke.png')

    const cloudGeometry = new THREE.BufferGeometry()
    const cloudMaterial = new THREE.PointsMaterial({
      size: 50,
      map: cloudTexture,
      transparent: true,
      depthWrite: false,
    })

    // 创建多层云
    for (let p = 0; p < 50; p++) {
      const cloud = new THREE.Points(cloudGeometry, cloudMaterial)
      cloud.rotation.x = Math.random() * 6
      cloud.rotation.y = Math.random() * 6
      cloud.rotation.z = Math.random() * 6
      cloud.position.set(Math.random() * 800 - 400, 500, Math.random() * 500 - 450)
      this.cloudParticles.push(cloud)
      this.scene.add(cloud)
    }
  }

  // 创建雨滴
  createRain() {
    const rainCount = 15000
    const vertices = new Float32Array(rainCount * 3)

    for (let i = 0; i < rainCount * 3; i += 3) {
      vertices[i] = Math.random() * 2000 - 1000 // x
      vertices[i + 1] = Math.random() * 2000 - 1000 // y
      vertices[i + 2] = Math.random() * 2000 - 1000 // z
    }

    const rainGeometry = new THREE.BufferGeometry()
    rainGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3))

    const rainMaterial = new THREE.PointsMaterial({
      color: 0xaaaaaa,
      size: 10,
      transparent: true,
      opacity: 0.6,
      sizeAttenuation: true,
      fog: true,
    })

    const raindrops = new THREE.Points(rainGeometry, rainMaterial)
    this.raindrops.push(raindrops)
    this.scene.add(raindrops)
  }

  // 设置阴天效果
  setCloudy() {
    this.isRaining = false
    this.ambient.intensity = 0.5
    // 移除雨滴
    this.raindrops.forEach((rain) => this.scene.remove(rain))
    this.raindrops = []

    if (this.cloudParticles.length === 0) {
      this.createClouds()
    }
  }

  // 设置雨天效果
  setRainy() {
    this.isRaining = true
    this.ambient.intensity = 0.3

    if (this.cloudParticles.length === 0) {
      this.createClouds()
    }
    if (this.raindrops.length === 0) {
      this.createRain()
    }
  }

  // 动画更新
  update() {
    if (this.cloudParticles.length > 0) {
      this.cloudParticles.forEach((cloud) => {
        cloud.rotation.z += 0.001
      })
    }

    if (this.isRaining) {
      this.raindrops.forEach((rain) => {
        const positions = rain.geometry.attributes.position.array as Float32Array
        for (let i = 0; i < positions.length; i += 3) {
          positions[i + 1] -= 1 // 雨滴下落速度

          // 如果雨滴落到地面，重置到顶部
          if (positions[i + 1] < -1000) {
            positions[i + 1] = 1000
          }
        }
        rain.geometry.attributes.position.needsUpdate = true
      })

      // 随机闪电效果
      if (Math.random() > 0.93 || this.flash.power > 100) {
        if (this.flash.power < 100) {
          this.flash.position.set(Math.random() * 400, 300 + Math.random() * 200, 100)
        }
        this.flash.power = 50 + Math.random() * 500
      }
    }
  }

  // 清理资源
  dispose() {
    this.cloudParticles.forEach((cloud) => {
      this.scene.remove(cloud)
      cloud.geometry.dispose()
      ;(cloud.material as THREE.Material).dispose()
    })

    this.raindrops.forEach((rain) => {
      this.scene.remove(rain)
      rain.geometry.dispose()
      ;(rain.material as THREE.Material).dispose()
    })

    this.scene.remove(this.flash)
    this.scene.remove(this.ambient)

    this.cloudParticles = []
    this.raindrops = []
  }
}
