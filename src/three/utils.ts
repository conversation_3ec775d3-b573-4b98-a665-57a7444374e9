/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-06-18
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-11
 */
import type { Vector3 } from 'three'
import Stats from 'three/examples/jsm/libs/stats.module.js'
import { viewer } from './viewer'

/**
 * Three.js 性能监控工具类
 */
export class PerformanceMonitor {
  private stats: Stats | null = null
  private isEnabled = false
  private animationFrameId: number | null = null

  /**
   * 初始化性能监控器
   * @param container - 容器元素，默认添加到 document.body
   * @returns Stats 实例
   */
  initialize(container: HTMLElement = document.body): Stats {
    if (this.stats) {
      return this.stats
    }

    this.stats = new Stats()
    this.stats.dom.style.position = 'fixed'
    this.stats.dom.style.top = '85px'
    this.stats.dom.style.left = '420px'
    this.stats.dom.style.zIndex = '1000'

    container.appendChild(this.stats.dom)
    this.show() // 初始化后自动显示并开始更新

    return this.stats
  }

  /**
   * 更新性能监控数据
   * 私有方法，由动画循环调用
   */
  private update = (): void => {
    if (this.isEnabled && this.stats) {
      this.stats.update()
      this.animationFrameId = requestAnimationFrame(this.update)
    }
  }

  /**
   * 显示性能监控面板并开始更新
   */
  show(): void {
    if (this.stats) {
      this.stats.dom.style.display = 'block'
      this.isEnabled = true
      // 开始更新循环
      if (!this.animationFrameId) {
        this.update()
      }
    }
  }

  /**
   * 隐藏性能监控面板并停止更新
   */
  hide(): void {
    if (this.stats) {
      this.stats.dom.style.display = 'none'
      this.isEnabled = false
      // 停止更新循环
      if (this.animationFrameId !== null) {
        cancelAnimationFrame(this.animationFrameId)
        this.animationFrameId = null
      }
    }
  }

  /**
   * 销毁性能监控器
   */
  dispose(): void {
    this.hide() // 确保停止更新循环
    if (this.stats) {
      this.stats.dom.remove()
      this.stats = null
    }
  }
}

// 导出性能监控器单例
export const performanceMonitor = new PerformanceMonitor()

/**
 * 世界坐标转屏幕坐标
 * @param pos 世界坐标
 * @returns 屏幕坐标
 */
export const wordToScreen = (pos: Vector3) => {
  const camera = viewer.getCamera()
  const renderer = viewer.getRenderer()
  const canvas = renderer.domElement

  // 复制一份，避免污染原始点
  const ndc = pos.clone().project(camera)
  // NDC [-1,1] 转换为屏幕像素
  const x = ((ndc.x + 1) / 2) * canvas.clientWidth
  const y = ((-ndc.y + 1) / 2) * canvas.clientHeight
  return { x, y }
}
