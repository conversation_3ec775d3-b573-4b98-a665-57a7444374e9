/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-06-18
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-24
 */

import { sceneConfig } from '@/config/scene'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { threeJSEvents, initThreeJSEvents } from './evens'
import { initHighlightManager } from './highLight'
import { labelManager } from './label'
import { Skybox } from './skybox'

class Viewer {
  public scene: THREE.Scene
  public camera: THREE.PerspectiveCamera
  public renderer: THREE.WebGLRenderer
  public composers: any[] = []
  public controls: OrbitControls
  private _inited: boolean = false
  public skybox: Skybox
  public sunLight?: THREE.DirectionalLight
  // public weatherSystem: WeatherSystem
  private container: HTMLDivElement
  private renderCallbacks: Array<() => void> = []
  private animateId: number | null = null
  private keyStates: { [key: string]: boolean } = {} // 添加按键状态对象
  private baseMoveSpeed: number = 2 // 基础移动速度，降低为2
  private minMoveSpeed: number = 0.01 // 最小移动速度，降低为0.5
  private maxMoveSpeed: number = 10 // 最大移动速度
  private heightFactor: number = 0.015 // 高度影响因子，降低为0.03

  // 添加边界限制相关属性
  private readonly BOUNDS = {
    minX: -50,
    maxX: 50,
    minY: -50,
    maxY: 50,
    minZ: -50,
    maxZ: 50,
  }

  /**
   * 限制相机位置在边界范围内
   */
  private clampCameraPosition(): void {
    const position = this.camera.position
    const target = this.controls.target

    // 限制相机位置
    position.x = Math.max(this.BOUNDS.minX, Math.min(this.BOUNDS.maxX, position.x))
    position.y = Math.max(this.BOUNDS.minY, Math.min(this.BOUNDS.maxY, position.y))
    position.z = Math.max(this.BOUNDS.minZ, Math.min(this.BOUNDS.maxZ, position.z))

    // 限制控制器目标点
    target.x = Math.max(this.BOUNDS.minX, Math.min(this.BOUNDS.maxX, target.x))
    target.y = Math.max(this.BOUNDS.minY, Math.min(this.BOUNDS.maxY, target.y))
    target.z = Math.max(this.BOUNDS.minZ, Math.min(this.BOUNDS.maxZ, target.z))
  }

  /**
   * 获取当前边界设置
   */
  public getBounds() {
    return { ...this.BOUNDS }
  }

  /**
   * 设置边界范围
   * @param bounds 新的边界设置
   */
  public setBounds(bounds: {
    minX?: number
    maxX?: number
    minY?: number
    maxY?: number
    minZ?: number
    maxZ?: number
  }) {
    Object.assign(this.BOUNDS, bounds)
    // 立即应用边界限制
    this.clampCameraPosition()
  }

  /**
   * 检查相机是否在边界范围内
   */
  public isCameraInBounds(): boolean {
    const position = this.camera.position
    return (
      position.x >= this.BOUNDS.minX &&
      position.x <= this.BOUNDS.maxX &&
      position.y >= this.BOUNDS.minY &&
      position.y <= this.BOUNDS.maxY &&
      position.z >= this.BOUNDS.minZ &&
      position.z <= this.BOUNDS.maxZ
    )
  }

  constructor(container: HTMLDivElement) {
    this.container = container
    this.scene = this.createScene()
    this.camera = this.createCamera()
    this.renderer = this.createRenderer()
    this.controls = this.createControls()
    this.skybox = new Skybox(this.scene, this.renderer)
    // this.weatherSystem = new WeatherSystem(this.scene, this.camera)

    this.setupLights()
    this.setupHelpers()
    this.setupEventListeners()
    this.setupKeyboardControls() // 添加键盘控制设置

    // 添加天气系统的更新到渲染回调
    // this.addRenderCallback(() => {
    //   this.weatherSystem.update()
    // })
    this.animate()
    this._inited = true
  }

  public get isInited(): boolean {
    return this._inited
  }

  private createScene(): THREE.Scene {
    const scene = new THREE.Scene()
    return scene
  }

  private createCamera(): THREE.PerspectiveCamera {
    const camera = new THREE.PerspectiveCamera(
      sceneConfig.fov,
      this.container.clientWidth / this.container.clientHeight,
      0.1,
      10000,
    )
    camera.position.set(...sceneConfig.center.position)
    return camera
  }

  private createRenderer(): THREE.WebGLRenderer {
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
    })
    renderer.setSize(this.container.clientWidth, this.container.clientHeight)
    renderer.setPixelRatio(window.devicePixelRatio)
    renderer.toneMapping = THREE.ACESFilmicToneMapping
    renderer.toneMappingExposure = 0.6
    renderer.outputColorSpace = THREE.SRGBColorSpace
    // 开启阴影映射以支持真实遮挡
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
    // 调整阴影透明度，使阴影更浅
    renderer.shadowMap.autoUpdate = true
    this.container.appendChild(renderer.domElement)
    return renderer
  }

  private createControls(): OrbitControls {
    const controls = new OrbitControls(this.camera, this.renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.1

    // 设置鼠标按键映射
    controls.mouseButtons = {
      LEFT: THREE.MOUSE.PAN, // 左键平移
      RIGHT: THREE.MOUSE.ROTATE, // 右键旋转
      MIDDLE: THREE.MOUSE.DOLLY, // 中键缩放
    }
    controls.minPolarAngle = 0
    controls.maxPolarAngle = 1.4
    controls.target.set(...sceneConfig.center.target)

    // 添加边界限制事件监听器
    controls.addEventListener('change', () => {
      this.clampCameraPosition()
    })

    return controls
  }

  private setupLights(): void {
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.3)
    this.scene.add(ambientLight)

    const sunLight = new THREE.DirectionalLight(0xffffff, 10)
    this.sunLight = sunLight
    sunLight.position.set(5.267389289682579, 250, 40.61818091545023) // 光源位置（模拟太阳方向）
    sunLight.castShadow = true // 启用阴影

    // 优化阴影质量（重要！）
    sunLight.shadow.mapSize.width = 2048
    sunLight.shadow.mapSize.height = 2048
    sunLight.shadow.camera.near = 0.5
    sunLight.shadow.camera.far = 400
    sunLight.shadow.camera.left = -50
    sunLight.shadow.camera.right = 50
    sunLight.shadow.camera.top = 50
    sunLight.shadow.camera.bottom = -50
    sunLight.shadow.bias = -0.0005
    // 调整阴影强度，使阴影更浅
    sunLight.shadow.intensity = 0.6
    this.scene.add(sunLight)

    const pointsLights = [
      // ups机房吊顶灯
      [-16.2089747364636, 7.3531613326223202, -22.94184885981561],
      // ups机房正面灯
      [-16.814834795355168, 7.35340328208740237, -20.83949958918373],
      // 信号机房吊顶灯
      [-8.231, 6.675, -16.116],
    ]
    pointsLights.forEach((point) => {
      const pointLight = new THREE.PointLight(0xffffff, 3.14, 10, 1)
      pointLight.position.set(point[0], point[1], point[2])
      pointLight.castShadow = true
      pointLight.shadow.mapSize.width = 2048
      pointLight.shadow.mapSize.height = 2048
      pointLight.shadow.bias = -0.001
      // 调整点光源阴影强度，使阴影更浅
      pointLight.shadow.intensity = 0.5
      this.scene.add(pointLight)
    })
  }

  private setupHelpers(): void {
    // const axesHelper = new THREE.AxesHelper(100)
    // this.scene.add(axesHelper)
    // const gridHelper = new THREE.GridHelper(100, 100)
    // this.scene.add(gridHelper)
  }

  private setupEventListeners(): void {
    window.addEventListener('resize', () => {
      this.camera.aspect = this.container.clientWidth / this.container.clientHeight
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)

      // 触发场景大小变化事件
      threeJSEvents.publish('scene:resize', {
        width: this.container.clientWidth,
        height: this.container.clientHeight,
      })
    })
  }

  // 添加键盘控制设置方法
  private setupKeyboardControls(): void {
    // 按键按下事件
    window.addEventListener('keydown', (event) => {
      this.keyStates[event.key.toLowerCase()] = true
    })

    // 按键释放事件
    window.addEventListener('keyup', (event) => {
      this.keyStates[event.key.toLowerCase()] = false
    })

    // 添加相机移动更新到渲染回调
    this.addRenderCallback(this.updateCameraPosition)
  }

  // 更新相机位置的方法
  private updateCameraPosition = (): void => {
    // 获取相机的朝向向量
    const direction = new THREE.Vector3()
    this.camera.getWorldDirection(direction)

    // 将方向向量投影到水平面上（去除Y分量），用于WS前后移动
    const horizontalDirection = new THREE.Vector3(direction.x, 0, direction.z).normalize()

    // 计算相机的右侧向量（叉乘）
    const right = new THREE.Vector3()
    right.crossVectors(this.camera.up, direction).normalize()

    // 计算相机的上方向量
    const up = new THREE.Vector3(0, 1, 0)

    // 根据相机高度动态调整移动速度
    const cameraHeight = Math.abs(this.camera.position.y)
    const moveSpeed = Math.max(
      this.minMoveSpeed,
      Math.min(this.maxMoveSpeed, cameraHeight * this.heightFactor),
    )

    // 根据按键状态更新相机位置
    if (this.keyStates['w']) {
      // 向前移动 (W) - 只在水平面上移动
      this.camera.position.addScaledVector(horizontalDirection, moveSpeed)
      this.controls.target.addScaledVector(horizontalDirection, moveSpeed)
    }
    if (this.keyStates['s']) {
      // 向后移动 (S) - 只在水平面上移动
      this.camera.position.addScaledVector(horizontalDirection, -moveSpeed)
      this.controls.target.addScaledVector(horizontalDirection, -moveSpeed)
    }
    if (this.keyStates['a']) {
      // 向左移动 (A)
      this.camera.position.addScaledVector(right, moveSpeed)
      this.controls.target.addScaledVector(right, moveSpeed)
    }
    if (this.keyStates['d']) {
      // 向右移动 (D)
      this.camera.position.addScaledVector(right, -moveSpeed)
      this.controls.target.addScaledVector(right, -moveSpeed)
    }
    if (this.keyStates['q']) {
      // 向上移动 (Q)
      this.camera.position.addScaledVector(up, moveSpeed)
      this.controls.target.addScaledVector(up, moveSpeed)
    }
    if (this.keyStates['e']) {
      // 向下移动 (E)
      this.camera.position.addScaledVector(up, -moveSpeed)
      this.controls.target.addScaledVector(up, -moveSpeed)
    }

    // 在键盘移动后检查并限制边界
    this.clampCameraPosition()
  }

  public animate = (): void => {
    this.controls.update() // 这行已经存在，确保 damping 效果生效

    // 执行所有注册的渲染回调函数
    for (const callback of this.renderCallbacks) {
      callback()
    }
    this.renderer.render(this.scene, this.camera)
    this.composers.forEach((composer) => {
      composer.render()
    })
    this.animateId = requestAnimationFrame(this.animate)
  }

  /**
   * 添加渲染回调函数
   * @param callback 在每帧渲染前执行的回调函数
   * @returns 回调函数索引，可用于移除回调
   */
  public addRenderCallback(callback: () => void): number {
    this.renderCallbacks.push(callback)
    return this.renderCallbacks.length - 1
  }

  /**
   * 移除渲染回调函数
   * @param index 回调函数索引
   * @returns 是否成功移除
   */
  public removeRenderCallback(index: number): boolean {
    if (index >= 0 && index < this.renderCallbacks.length) {
      this.renderCallbacks.splice(index, 1)
      return true
    }
    return false
  }

  public getScene(): THREE.Scene {
    return this.scene
  }

  public getCamera(): THREE.PerspectiveCamera {
    return this.camera
  }

  public getRenderer(): THREE.WebGLRenderer {
    return this.renderer
  }

  public getControls(): OrbitControls {
    return this.controls
  }

  /**
   * 加载模型
   * @param url 模型URL
   * @param onProgress 加载进度回调
   * @returns Promise<THREE.Object3D>
   */
  public loadModel(
    url: string,
    onProgress?: (event: ProgressEvent) => void,
  ): Promise<THREE.Object3D> {
    return new Promise((resolve, reject) => {
      const loader = new THREE.ObjectLoader()

      loader.load(
        url,
        (object) => {
          this.scene.add(object)
          // 发布模型加载完成事件
          threeJSEvents.modelLoaded(object, url)
          resolve(object)
        },
        onProgress,
        (error) => {
          // 发布模型加载失败事件
          const err = error instanceof Error ? error : new Error(String(error))
          threeJSEvents.modelError(err, url)
          reject(err)
        },
      )
    })
  }

  /**
   * 注册事件监听
   * @param eventName 事件名称
   * @param callback 回调函数
   * @param once 是否只触发一次
   */
  public on<K extends keyof import('./evens').ThreeJSEventMap>(
    eventName: K,
    callback: (data: import('./evens').ThreeJSEventMap[K]) => void,
    once = false,
  ): void {
    threeJSEvents.subscribe(eventName, callback, once)
  }

  /**
   * 取消事件监听
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public off<K extends keyof import('./evens').ThreeJSEventMap>(
    eventName: K,
    callback: (data: import('./evens').ThreeJSEventMap[K]) => void,
  ): void {
    threeJSEvents.unsubscribe(eventName, callback)
  }

  /**
   * 停止动画循环
   */
  public stopAnimation(): void {
    if (this.animateId !== null) {
      cancelAnimationFrame(this.animateId)
      this.animateId = null
    }
  }
}

export let viewer: Viewer

export const initViewer = (container: HTMLDivElement) => {
  viewer = new Viewer(container)
  initThreeJSEvents()
  initHighlightManager()
  // 初始化标签管理器
  labelManager.initialize(container)
  // @ts-ignore
  window.threeViewer = viewer
}
