/*
 * @Description: ThreeJS事件管理模块
 * @Author: yucheng
 * @Date: 2025-06-19
 * @LastEditors: '余承'
 * @LastEditTime: 2025-07-13
 */

import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
import Pubsub from '@/utils/pubsub'
import { viewer } from './viewer'

// 定义ThreeJS事件类型
export interface ThreeJSEventMap {
  // 相机事件
  'camera:change': { camera: THREE.Camera }
  'camera:zoom': { zoom: number }
  'camera:move': { position: THREE.Vector3 }

  // 控制器事件
  'controls:start': { controls: OrbitControls }
  'controls:end': { controls: OrbitControls }
  'controls:change': { controls: OrbitControls }

  // 鼠标事件
  'mouse:move': { event: MouseEvent; intersects: THREE.Intersection[] }
  'mouse:click': { event: MouseEvent; intersects: THREE.Intersection[] }
  'mouse:dblclick': { event: MouseEvent; intersects: THREE.Intersection[] }

  // 对象事件
  'object:hover': { object: THREE.Object3D; point: THREE.Vector3; event: MouseEvent }
  'object:click': { object: THREE.Object3D; point: THREE.Vector3; event: MouseEvent }
  'object:dblclick': { object: THREE.Object3D; point: THREE.Vector3 }
  'object:hoverend': { object: THREE.Object3D; event: MouseEvent }

  // 场景事件
  'scene:loaded': { scene: THREE.Scene }
  'scene:update': { scene: THREE.Scene; deltaTime: number }
  'scene:resize': { width: number; height: number }

  // 模型事件
  'model:loaded': { object: THREE.Object3D; url: string }
  'model:error': { error: Error; url: string }

  // 设备显示相关事件
  updateDeviceDisplay: {
    systems: string[]
    floor: { id: string; name: string }
  }

  // 自定义事件
  [key: string]: any

  // 天空盒相关事件
  'skybox:loaded': { texture: THREE.Texture }
  'skybox:error': { error: any }
}

/**
 * ThreeJS事件管理类
 * 基于PubSub模式，集中管理ThreeJS的各类事件
 */
class ThreeJSEventManager {
  private pubsub: Pubsub<ThreeJSEventMap>
  private raycaster: THREE.Raycaster
  private mouse: THREE.Vector2
  private hoveredObject: THREE.Object3D | null = null
  private clickedObject: THREE.Object3D | null = null
  private isInitialized = false

  constructor() {
    this.pubsub = new Pubsub<ThreeJSEventMap>()
    this.raycaster = new THREE.Raycaster()
    this.mouse = new THREE.Vector2()
  }

  /**
   * 初始化事件管理器
   */
  public init(): void {
    if (this.isInitialized) return

    const renderer = viewer.getRenderer()
    const controls = viewer.getControls()
    const camera = viewer.getCamera()
    const scene = viewer.getScene()

    // 添加控制器事件监听
    controls.addEventListener('start', () => {
      this.pubsub.publish('controls:start', { controls })
    })

    controls.addEventListener('end', () => {
      this.pubsub.publish('controls:end', { controls })
    })

    controls.addEventListener('change', () => {
      this.pubsub.publish('controls:change', { controls })
      this.pubsub.publish('camera:change', { camera })
      this.pubsub.publish('camera:move', { position: camera.position.clone() })
    })

    // 添加窗口大小变化事件
    window.addEventListener('resize', () => {
      this.pubsub.publish('scene:resize', {
        width: renderer.domElement.clientWidth,
        height: renderer.domElement.clientHeight,
      })
    })

    // 添加鼠标事件
    renderer.domElement.addEventListener('mousemove', this.onMouseMove.bind(this))
    renderer.domElement.addEventListener('click', this.onMouseClick.bind(this))
    renderer.domElement.addEventListener('dblclick', this.onMouseDblClick.bind(this))

    // 发布场景加载完成事件
    this.pubsub.publish('scene:loaded', { scene })

    // 添加动画循环更新事件
    let lastTime = 0
    const animate = (time: number) => {
      const deltaTime = time - lastTime
      lastTime = time

      this.pubsub.publish('scene:update', { scene, deltaTime })
      requestAnimationFrame(animate)
    }
    requestAnimationFrame(animate)

    this.isInitialized = true
  }

  /**
   * 鼠标移动事件处理
   */
  private onMouseMove(event: MouseEvent): void {
    if (!viewer) return

    const renderer = viewer.getRenderer()
    const camera = viewer.getCamera()
    const scene = viewer.getScene()

    // 计算鼠标在归一化设备坐标中的位置
    const rect = renderer.domElement.getBoundingClientRect()
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // 发射射线
    this.raycaster.setFromCamera(this.mouse, camera)
    const allIntersects = this.raycaster.intersectObjects(scene.children, true)
    // 过滤不可见的对象
    const intersects = allIntersects.filter((intersect) => {
      let obj = intersect.object
      while (obj) {
        if (!obj.visible) return false
        obj = obj.parent as THREE.Object3D
      }
      return true
    })

    // 发布鼠标移动事件
    this.pubsub.publish('mouse:move', { event, intersects })

    // 处理对象悬停事件
    if (intersects.length > 0) {
      const firstIntersect = intersects[0]
      const object = firstIntersect.object

      if (this.hoveredObject !== object) {
        if (this.hoveredObject) {
          // 离开之前悬停的对象
          this.pubsub.publish('object:leave', { object: this.hoveredObject })
        }

        // 进入新的对象
        this.hoveredObject = object
        this.pubsub.publish('object:hover', {
          object,
          point: firstIntersect.point,
          event,
        })
      }
    } else if (this.hoveredObject) {
      // 离开之前悬停的对象，没有新的对象
      this.pubsub.publish('object:hoverend', {
        object: this.hoveredObject,
        event,
      })
      this.hoveredObject = null
    }
  }

  /**
   * 鼠标点击事件处理
   */
  private onMouseClick(event: MouseEvent): void {
    if (!viewer) return

    const camera = viewer.getCamera()
    const scene = viewer.getScene()

    // 发射射线
    this.raycaster.setFromCamera(this.mouse, camera)
    const allIntersects = this.raycaster.intersectObjects(scene.children, true)
    // 过滤不可见的对象
    const intersects = allIntersects.filter((intersect) => {
      let obj = intersect.object
      while (obj) {
        if (!obj.visible) return false
        obj = obj.parent as THREE.Object3D
      }
      return true
    })

    // 发布鼠标点击事件
    this.pubsub.publish('mouse:click', { event, intersects })

    // 处理对象点击事件
    if (intersects.length > 0) {
      const firstIntersect = intersects[0]
      const object = firstIntersect.object

      this.clickedObject = object
      this.pubsub.publish('object:click', {
        object,
        point: firstIntersect.point,
        event,
      })
    }
  }

  /**
   * 鼠标双击事件处理
   */
  private onMouseDblClick(event: MouseEvent): void {
    if (!viewer) return

    const camera = viewer.getCamera()
    const scene = viewer.getScene()

    // 发射射线
    this.raycaster.setFromCamera(this.mouse, camera)
    const allIntersects = this.raycaster.intersectObjects(scene.children, true)
    // 过滤不可见的对象
    const intersects = allIntersects.filter((intersect) => {
      let obj = intersect.object
      while (obj) {
        if (!obj.visible) return false
        obj = obj.parent as THREE.Object3D
      }
      return true
    })

    // 发布鼠标双击事件
    this.pubsub.publish('mouse:dblclick', { event, intersects })

    // 处理对象双击事件
    if (intersects.length > 0) {
      const firstIntersect = intersects[0]
      const object = firstIntersect.object

      this.pubsub.publish('object:dblclick', {
        object,
        point: firstIntersect.point,
      })
    }
  }

  /**
   * 订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   * @param once 是否只触发一次
   */
  public subscribe<K extends keyof ThreeJSEventMap>(
    eventName: K,
    callback: (data: ThreeJSEventMap[K]) => void,
    once = false,
  ): void {
    this.pubsub.subscribe(eventName, callback, once)
  }

  /**
   * 取消订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public unsubscribe<K extends keyof ThreeJSEventMap>(
    eventName: K,
    callback: (data: ThreeJSEventMap[K]) => void,
  ): void {
    this.pubsub.unsubscribe(eventName, callback)
  }

  /**
   * 发布事件
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public publish<K extends keyof ThreeJSEventMap>(eventName: K, data: ThreeJSEventMap[K]): void {
    this.pubsub.publish(eventName, data)
  }

  /**
   * 清空所有事件订阅
   */
  public clear(): void {
    this.pubsub.clear()
  }

  /**
   * 加载模型完成后发布事件
   * @param model 加载的模型
   * @param url 模型URL
   */
  public modelLoaded(model: THREE.Object3D, url: string): void {
    this.pubsub.publish('model:loaded', { object: model, url })
  }

  /**
   * 加载模型失败后发布事件
   * @param error 错误信息
   * @param url 模型URL
   */
  public modelError(error: Error, url: string): void {
    this.pubsub.publish('model:error', { error, url })
  }
}

// 创建单例实例
export const threeJSEvents = new ThreeJSEventManager()

// 导出初始化函数
export const initThreeJSEvents = () => {
  threeJSEvents.init()
}
