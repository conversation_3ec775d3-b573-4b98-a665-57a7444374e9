import { useMouseTip } from '@/hooks/useMouseTip'
import * as THREE from 'three'
import type { Ref } from 'vue'

export interface DrawOptions {
  color?: string // 绘制颜色
  opacity?: number // 透明度
  lineWidth?: number // 线宽
  planeNormal?: THREE.Vector3 // 绘制平面法线
  planeConstant?: number // 绘制平面常数
}

export class GeometryDrawer {
  private scene: THREE.Scene
  private camera: THREE.Camera
  private renderer: THREE.WebGLRenderer
  private domElement: HTMLElement

  private points: THREE.Vector3[] = [] // 存储顶点
  private tempPoint: THREE.Vector3 | null = null // 临时点（鼠标位置）

  private previewLine: THREE.Object3D | null = null // 预览线
  private previewMesh: THREE.Mesh | null = null // 预览面

  private raycaster = new THREE.Raycaster()
  private mouse = new THREE.Vector2()
  private plane: THREE.Plane

  private isDrawing = false
  private onComplete: ((points: THREE.Vector3[]) => void) | null = null
  private onCancel: (() => void) | null = null
  // 鼠标按下位置判断相关变量
  private rightClickStartX = 0
  private rightClickStartY = 0
  private leftClickStartX = 0
  private leftClickStartY = 0
  private clickMoveThreshold = 5 // 像素，判断点击与拖动的阈值

  private tipText: Ref<string>
  private showTip: Ref<boolean>

  private defaultOptions: Required<DrawOptions> = {
    color: '#00ff00',
    opacity: 1,
    lineWidth: 40,
    planeNormal: new THREE.Vector3(0, 1, 0),
    planeConstant: 0,
  }

  private options: Required<DrawOptions>

  constructor(
    scene: THREE.Scene,
    camera: THREE.Camera,
    renderer: THREE.WebGLRenderer,
    options?: DrawOptions,
  ) {
    this.scene = scene
    this.camera = camera
    this.renderer = renderer
    this.domElement = renderer.domElement

    this.options = { ...this.defaultOptions, ...options }

    // 创建绘制平面
    this.plane = new THREE.Plane(this.options.planeNormal, this.options.planeConstant)
    const { tipText, showTip } = useMouseTip()
    this.tipText = tipText
    this.showTip = showTip
    // 绑定事件处理函数的this上下文
    this.onClick = this.onClick.bind(this)
    this.onMouseMove = this.onMouseMove.bind(this)
    this.onRightClick = this.onRightClick.bind(this)
    this.onRightClickUp = this.onRightClickUp.bind(this)
    this.onDblClick = this.onDblClick.bind(this)
    this.onLeftMouseDown = this.onLeftMouseDown.bind(this)
  }

  /**
   * 开始绘制
   * @param callback 绘制完成后的回调函数，返回多边形顶点数组
   */
  public startDraw(callback: (points: THREE.Vector3[]) => void, onCancel?: () => void): void {
    if (this.isDrawing) return
    this.tipText.value = '单击左键添加顶点'
    this.showTip.value = true
    this.isDrawing = true
    this.points = []
    this.onComplete = callback
    this.onCancel = onCancel ?? null
    // 添加事件监听
    this.domElement.addEventListener('click', this.onClick)
    this.domElement.addEventListener('mousemove', this.onMouseMove)
    this.domElement.addEventListener('mousedown', this.onRightClick)
    this.domElement.addEventListener('mousedown', this.onLeftMouseDown)
    this.domElement.addEventListener('mouseup', this.onRightClickUp)
    this.domElement.addEventListener('dblclick', this.onDblClick)
  }

  /**
   * 取消绘制
   */
  public cancelDraw(): void {
    if (!this.isDrawing) return

    this.cleanup()
    this.isDrawing = false
  }

  /**
   * 清理绘制相关资源
   */
  private cleanup(): void {
    // 移除事件监听
    this.domElement.removeEventListener('click', this.onClick)
    this.domElement.removeEventListener('mousemove', this.onMouseMove)
    this.domElement.removeEventListener('mousedown', this.onRightClick)
    this.domElement.removeEventListener('mousedown', this.onLeftMouseDown)
    this.domElement.removeEventListener('mouseup', this.onRightClickUp)
    this.domElement.removeEventListener('dblclick', this.onDblClick)
    this.tipText.value = ''
    this.showTip.value = false
    // 清除预览对象
    if (this.previewLine) {
      this.scene.remove(this.previewLine)

      // 根据对象类型处理几何体释放
      if (this.previewLine instanceof THREE.Line) {
        this.previewLine.geometry.dispose()
      } else if (this.previewLine instanceof THREE.Mesh) {
        this.previewLine.geometry.dispose()
        if (this.previewLine.material instanceof THREE.Material) {
          this.previewLine.material.dispose()
        } else if (Array.isArray(this.previewLine.material)) {
          this.previewLine.material.forEach((material) => material.dispose())
        }
      } else if (this.previewLine instanceof THREE.Group) {
        // 处理Group类型的对象
        this.previewLine.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose()
            if (child.material instanceof THREE.Material) {
              child.material.dispose()
            } else if (Array.isArray(child.material)) {
              child.material.forEach((material) => material.dispose())
            }
          }
        })
      }

      this.previewLine = null
    }

    if (this.previewMesh) {
      this.scene.remove(this.previewMesh)
      this.previewMesh.geometry.dispose()
      if (this.previewMesh.material instanceof THREE.Material) {
        this.previewMesh.material.dispose()
      } else if (Array.isArray(this.previewMesh.material)) {
        this.previewMesh.material.forEach((material) => material.dispose())
      }
      this.previewMesh = null
    }

    this.points = []
    this.tempPoint = null
  }

  /**
   * 左键按下事件处理
   */
  private onLeftMouseDown(event: MouseEvent): void {
    if (event.button !== 0) return
    // 记录左键按下的位置
    this.leftClickStartX = event.clientX
    this.leftClickStartY = event.clientY
  }

  /**
   * 鼠标点击事件处理
   */
  private onClick(event: MouseEvent): void {
    // 防止事件冒泡
    event.preventDefault()

    // 排除右键点击
    if (event.button !== 0) return

    // 计算左键按下和释放位置的距离
    const dx = event.clientX - this.leftClickStartX
    const dy = event.clientY - this.leftClickStartY
    const distance = Math.sqrt(dx * dx + dy * dy)

    // 如果移动距离大于阈值，认为是拖动操作而非点击，忽略添加顶点
    if (distance > this.clickMoveThreshold) {
      console.log('忽略添加顶点，移动距离:', distance)
      return
    }

    // 获取鼠标点击位置的3D坐标
    const point = this.getIntersectionPoint(event)
    if (!point) return

    // 添加顶点
    this.points.push(point)
    this.tipText.value = '单击左键添加顶点,双击左键完成绘制'
    // 更新预览
    this.updatePreview()
  }

  /**
   * 鼠标移动事件处理
   */
  private onMouseMove(event: MouseEvent): void {
    event.preventDefault()

    // 如果还没有添加任何点，不需要预览
    if (this.points.length === 0) return

    // 获取鼠标当前位置的3D坐标
    const point = this.getIntersectionPoint(event)
    if (!point) return

    this.tempPoint = point

    // 更新预览
    this.updatePreview()
  }

  /**
   * 鼠标右键点击事件处理
   */
  private onRightClick(event: MouseEvent): void {
    if (event.button !== 2) return
    // 记录右键按下的位置
    this.rightClickStartX = event.clientX
    this.rightClickStartY = event.clientY

    // 阻止默认的右键菜单
    event.preventDefault()
  }

  /**
   * 鼠标右键释放事件处理
   */
  private onRightClickUp(event: MouseEvent): void {
    // 只处理右键释放
    if (event.button !== 2) return

    // 计算右键按下和释放位置的距离
    const dx = event.clientX - this.rightClickStartX
    const dy = event.clientY - this.rightClickStartY
    const distance = Math.sqrt(dx * dx + dy * dy)

    // 如果移动距离小于阈值，认为是点击操作而非场景移动
    if (distance < this.clickMoveThreshold) {
      console.log('取消绘制，移动距离:', distance)
      // 取消绘制
      // this.cancelDraw()
      // this.onCancel?.()
    }
  }

  /**
   * 鼠标双击事件处理
   */
  private onDblClick(event: MouseEvent): void {
    event.preventDefault()

    // 需要至少有两个点才能形成多边形
    if (this.points.length < 3) return

    // 完成绘制
    if (this.onComplete) {
      const result = [...this.points] // 复制点数组
      this.onComplete(result)
    }

    // 清理资源
    this.cleanup()
    this.isDrawing = false
  }

  /**
   * 获取鼠标位置与平面的交点
   */
  private getIntersectionPoint(event: MouseEvent): THREE.Vector3 | null {
    // 计算鼠标在归一化设备坐标中的位置
    const rect = this.domElement.getBoundingClientRect()
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // 设置射线
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 首先尝试与场景中的物体相交
    const intersects = this.raycaster.intersectObjects(this.scene.children, true)
    if (intersects.length > 0) {
      // 使用第一个交点的位置
      return intersects[0].point.clone()
    }

    // 如果没有与物体相交，则使用与平面的交点
    const intersectionPoint = new THREE.Vector3()
    const result = this.raycaster.ray.intersectPlane(this.plane, intersectionPoint)

    return result ? intersectionPoint : null
  }

  /**
   * 更新预览线和面
   */
  private updatePreview(): void {
    // 如果没有临时点，不需要更新
    if (!this.tempPoint) return

    // 清除之前的预览
    if (this.previewLine) {
      this.scene.remove(this.previewLine)

      // 根据对象类型处理几何体释放
      if (this.previewLine instanceof THREE.Line) {
        this.previewLine.geometry.dispose()
      } else if (this.previewLine instanceof THREE.Mesh) {
        this.previewLine.geometry.dispose()
        if (this.previewLine.material instanceof THREE.Material) {
          this.previewLine.material.dispose()
        } else if (Array.isArray(this.previewLine.material)) {
          this.previewLine.material.forEach((material) => material.dispose())
        }
      } else if (this.previewLine instanceof THREE.Group) {
        // 处理Group类型的对象
        this.previewLine.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose()
            if (child.material instanceof THREE.Material) {
              child.material.dispose()
            } else if (Array.isArray(child.material)) {
              child.material.forEach((material) => material.dispose())
            }
          }
        })
      }

      this.previewLine = null
    }

    if (this.previewMesh) {
      this.scene.remove(this.previewMesh)
      this.previewMesh.geometry.dispose()
      if (this.previewMesh.material instanceof THREE.Material) {
        this.previewMesh.material.dispose()
      } else if (Array.isArray(this.previewMesh.material)) {
        this.previewMesh.material.forEach((material) => material.dispose())
      }
      this.previewMesh = null
    }

    // 创建预览线的点
    const linePoints = [...this.points, this.tempPoint]

    // 如果至少有两个点，创建线条
    if (linePoints.length >= 2) {
      // 根据线宽选择不同的绘制方式
      if (this.options.lineWidth > 2) {
        // 创建一个组来容纳所有线段
        const lineGroup = new THREE.Group()

        // 为每对相邻点创建一个粗线段
        for (let i = 0; i < linePoints.length - 1; i++) {
          const start = linePoints[i]
          const end = linePoints[i + 1]

          // 创建一个方向向量
          const direction = new THREE.Vector3().subVectors(end, start)
          const length = direction.length()

          // 创建一个圆柱体作为线段
          const geometry = new THREE.CylinderGeometry(
            this.options.lineWidth * 0.01, // 顶部半径
            this.options.lineWidth * 0.01, // 底部半径
            length, // 高度
            8, // 圆周分段
            1, // 高度分段
            false, // 是否开口
          )

          // 旋转几何体，使其沿着Y轴方向
          geometry.rotateX(Math.PI / 2)

          const material = new THREE.MeshBasicMaterial({
            color: this.options.color,
            opacity: this.options.opacity,
            transparent: true,
          })

          const cylinder = new THREE.Mesh(geometry, material)

          // 计算线段的中点
          const midpoint = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5)
          cylinder.position.copy(midpoint)

          // 让圆柱体指向终点
          cylinder.lookAt(end)

          // 添加到组中
          lineGroup.add(cylinder)
        }

        this.previewLine = lineGroup
      } else {
        // 使用普通线条
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(linePoints)
        const lineMaterial = new THREE.LineBasicMaterial({
          color: this.options.color,
          linewidth: this.options.lineWidth,
          opacity: this.options.opacity,
          transparent: true,
        })

        this.previewLine = new THREE.Line(lineGeometry, lineMaterial)
      }

      this.scene.add(this.previewLine)
    }

    // 如果至少有三个点，创建面
    if (linePoints.length >= 3) {
      // 创建平面几何体
      const shape = new THREE.Shape()

      // 使用实际的 x,y,z 坐标来创建平面
      // 首先，确定绘制平面的最佳方向
      const normal = this.options.planeNormal.clone().normalize()
      let tangent, bitangent

      // 基于法线方向，确定切线和副切线
      if (Math.abs(normal.x) < 0.5) {
        tangent = new THREE.Vector3(1, 0, 0)
      } else {
        tangent = new THREE.Vector3(0, 1, 0)
      }

      bitangent = new THREE.Vector3().crossVectors(normal, tangent).normalize()
      tangent = new THREE.Vector3().crossVectors(bitangent, normal).normalize()

      // 将3D点投影到2D平面上
      const points2D = linePoints.map((point) => {
        const localPoint = point.clone()
        return {
          x: localPoint.dot(tangent),
          y: localPoint.dot(bitangent),
        }
      })

      shape.moveTo(points2D[0].x, points2D[0].y)
      for (let i = 1; i < points2D.length; i++) {
        shape.lineTo(points2D[i].x, points2D[i].y)
      }

      const geometry = new THREE.ShapeGeometry(shape)

      // 将2D几何体转换回3D空间
      const vertices = geometry.getAttribute('position').array
      for (let i = 0; i < vertices.length; i += 3) {
        const x = vertices[i]
        const y = vertices[i + 1]
        const z = vertices[i + 2]

        const worldPoint = new THREE.Vector3()
          .addScaledVector(tangent, x)
          .addScaledVector(bitangent, y)
          .addScaledVector(normal, z)

        vertices[i] = worldPoint.x
        vertices[i + 1] = worldPoint.y
        vertices[i + 2] = worldPoint.z
      }

      geometry.getAttribute('position').needsUpdate = true
      geometry.computeVertexNormals()

      const material = new THREE.MeshBasicMaterial({
        color: this.options.color,
        opacity: this.options.opacity / 2, // 面的透明度稍低
        transparent: true,
        side: THREE.DoubleSide,
      })

      this.previewMesh = new THREE.Mesh(geometry, material)
      this.scene.add(this.previewMesh)
    }
  }

  /**
   * 设置绘制选项
   */
  public setOptions(options: DrawOptions): void {
    this.options = { ...this.options, ...options }

    // 如果更新了平面参数，重新创建平面
    if (options.planeNormal || options.planeConstant !== undefined) {
      this.plane = new THREE.Plane(
        options.planeNormal || this.options.planeNormal,
        options.planeConstant !== undefined ? options.planeConstant : this.options.planeConstant,
      )
    }

    // 如果正在预览，更新预览样式
    if (this.isDrawing) {
      this.updatePreview()
    }
  }

  /**
   * 检查是否正在绘制
   */
  public isActive(): boolean {
    return this.isDrawing
  }
}
