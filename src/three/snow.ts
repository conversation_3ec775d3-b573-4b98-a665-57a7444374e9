import * as THREE from 'three'
import { viewer } from './viewer'

// 雪花系统状态枚举
enum SnowState {
  STOPPED = 'stopped',
  RUNNING = 'running',
  DESTROYED = 'destroyed',
}

export class Snow {
  public params = {
    snowEnabled: true,
    snowAmount: 0.7,
  }

  private animationId: number | null = null
  private pointMesh: THREE.Points | null = null
  private state: SnowState = SnowState.STOPPED
  private vertices: number[] = []
  private offset: number[] = []
  private geometry: THREE.BufferGeometry | null = null
  private material: THREE.PointsMaterial | null = null
  private postprocessing: any = {}

  // 配置参数
  private readonly config = {
    particleCount: 100000,
    size: 2,
    opacity: 0.6,
    moveSpeed: 0.5,
    bounds: {
      width: window.innerWidth,
      height: window.innerHeight,
      maxX: 20,
      minX: -20,
      maxY: 20,
      minY: 0,
      maxZ: 20,
      minZ: -20,
    },
  }

  constructor() {
    this.initGeometry()
    this.initMaterial()
    this.initMesh()
    this.initPostprocessing()
  }

  /**
   * 初始化几何体和粒子顶点数据
   */
  private initGeometry(): void {
    this.geometry = new THREE.BufferGeometry()
    this.vertices = []
    this.offset = []

    // 生成粒子位置和偏移数据
    for (let i = 0; i < this.config.particleCount; i++) {
      const x = 1000 * (Math.random() - 0.5)
      const y = 600 * Math.random()
      const z = 1000 * (Math.random() - 0.5)

      this.vertices.push(x, y, z)
      this.offset.push(Math.random() - 0.5, 0, Math.random() - 0.5)
    }

    this.geometry.setAttribute('position', new THREE.Float32BufferAttribute(this.vertices, 3))
  }

  /**
   * 初始化粒子材质
   */
  private initMaterial(): void {
    const texture = new THREE.TextureLoader().load('textures/snow.png')

    this.material = new THREE.PointsMaterial({
      size: this.config.size,
      depthTest: false,
      depthWrite: false,
      map: texture,
      transparent: true,
      blending: THREE.AdditiveBlending,
      opacity: this.config.opacity,
      sizeAttenuation: true,
      alphaTest: 0.01,
      vertexColors: false,
    })
  }

  /**
   * 初始化粒子网格
   */
  private initMesh(): void {
    if (!this.geometry || !this.material) {
      throw new Error('Geometry and material must be initialized first')
    }

    this.pointMesh = new THREE.Points(this.geometry, this.material)
    this.pointMesh.renderOrder = 1000
    viewer.scene.add(this.pointMesh)
  }

  /**
   * 初始化后处理
   */
  private initPostprocessing(): void {
    this.postprocessing.scene = new THREE.Scene()
    this.postprocessing.camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1)
    this.postprocessing.scene.add(this.postprocessing.camera)

    // 漫射渲染目标
    this.postprocessing.difusse = new THREE.WebGLRenderTarget(
      window.innerWidth,
      window.innerHeight,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType,
        colorSpace: THREE.SRGBColorSpace,
        depthBuffer: true,
        samples: 4,
        minFilter: THREE.NearestFilter,
        magFilter: THREE.NearestFilter,
        stencilBuffer: false,
      },
    )

    // G-Buffer渲染目标
    this.postprocessing.gBuffer = new THREE.WebGLRenderTarget(
      window.innerWidth,
      window.innerHeight,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType,
        depthBuffer: true,
        samples: 4,
        count: 2,
      },
    )

    // G-Buffer Pass
    this.postprocessing.gBufferPass = new THREE.ShaderMaterial({
      vertexShader: `
        out vec3 vNormal;
        out vec3 vWorldPosition;
        void main() {
            vNormal = normal;
            vec4 worldPosition = modelMatrix * vec4(position, 1.0);
            vWorldPosition = worldPosition.xyz;
            gl_Position = projectionMatrix * viewMatrix * worldPosition;
        }
      `,
      fragmentShader: `
        in vec3 vNormal;
        in vec3 vWorldPosition;
        layout(location = 0) out vec4 gPosition;
        layout(location = 1) out vec4 gNormal;
        void main() {
          gPosition = vec4(vWorldPosition, 1.0);
          gNormal = normalize(vec4(vNormal, 1.0));
        }
      `,
      glslVersion: '300 es',
    })

    // 最终材质
    this.postprocessing.finalMaterial = new THREE.ShaderMaterial({
      defines: { EMISSIVE: 10 },
      vertexShader: `
        out vec2 vUv;
        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        precision highp float;
        precision highp int;

        uniform sampler2D tPosition;
        uniform sampler2D tNormal;
        uniform sampler2D tDiffuse;
        uniform vec2 resolution;
        uniform float time;
        uniform vec3 uCameraPosition;
        uniform float snowAmount;
        uniform float snowNoise;
        uniform float snowEdge;

        in vec2 vUv;
        out vec4 fragColor;

        float rand(vec2 co) {
            return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
        }

        float noise(vec2 p) {
            vec2 ip = floor(p);
            vec2 fp = fract(p);
            float a = rand(ip);
            float b = rand(ip + vec2(1.0, 0.0));
            float c = rand(ip + vec2(0.0, 1.0));
            float d = rand(ip + vec2(1.0, 1.0));
            vec2 u = fp * fp * (3.0 - 2.0 * fp);
            return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
        }

        float fbm(vec2 p) {
            float total = 0.0;
            float amplitude = 1.0;
            for (int i = 0; i < 4; i++) {
                total += noise(p) * amplitude;
                p *= 2.0;
                amplitude *= 0.5;
            }
            return total;
        }

        void main() {
            vec3 position = texture(tPosition, vUv).rgb;
            vec3 normal = normalize(texture(tNormal, vUv).rgb);
            vec4 diffuseSample = texture(tDiffuse, vUv);
            vec3 diffuse = diffuseSample.rgb;
            if (diffuseSample.a < 0.01) discard;

            float snowFactor = max(0.0, dot(normal, vec3(0.0, 1.0, 0.0)));
            snowFactor = pow(snowFactor, 3.0);

            vec2 noiseCoord = position.xz * 0.5 + vec2(time * 0.05);
            float noiseVal = fbm(noiseCoord);
            snowFactor = clamp(snowFactor + (noiseVal - 0.5) * snowNoise, 0.0, 1.0);
            snowFactor *= snowAmount;

            vec2 texelSize = 1.0 / resolution;
            float depthCenter = texture(tPosition, vUv).z;
            float depthRight = texture(tPosition, vUv + vec2(texelSize.x, 0.0)).z;
            float depthBottom = texture(tPosition, vUv + vec2(0.0, texelSize.y)).z;
            float depthDiff = max(abs(depthCenter - depthRight), abs(depthCenter - depthBottom));
            snowFactor = max(snowFactor, smoothstep(0.0, 0.1, depthDiff) * snowEdge);

            vec3 snowColor = mix(vec3(0.95, 0.96, 0.98), vec3(1.0), noiseVal * 0.2);
            vec3 finalColor = mix(diffuse, snowColor, smoothstep(0.3, 0.7, snowFactor));

            if (snowFactor > 0.3) {
                vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
                vec3 viewDir = normalize(uCameraPosition - position);
                vec3 halfDir = normalize(lightDir + viewDir);
                float spec = pow(max(0.0, dot(normal, halfDir)), 32.0);
                finalColor += spec * 0.1 * vec3(1.0) * snowFactor;
            }

            finalColor = mix(finalColor, vec3(1.0), snowFactor * 0.3);
            fragColor = vec4(finalColor, 1.0);
        }
      `,
      glslVersion: '300 es',
      uniforms: {
        tPosition: { value: this.postprocessing.gBuffer.textures[0] },
        tNormal: { value: this.postprocessing.gBuffer.textures[1] },
        tDiffuse: { value: this.postprocessing.difusse.texture },
        resolution: { value: new THREE.Vector2(window.innerWidth, window.innerHeight) },
        time: { value: 0 },
        uCameraPosition: { value: new THREE.Vector3() },
        snowAmount: { value: 0.7 },
        snowNoise: { value: 0.3 },
        snowEdge: { value: 0.5 },
      },
    })

    this.postprocessing.quad = new THREE.Mesh(
      new THREE.PlaneGeometry(2.0, 2.0),
      this.postprocessing.finalMaterial,
    )
    this.postprocessing.scene.add(this.postprocessing.quad)
  }

  /**
   * 更新粒子位置
   */
  private updatePoints(): void {
    if (!this.pointMesh || !this.geometry) return

    for (let i = 1; i < this.vertices.length; i += 3) {
      this.vertices[i] -= this.config.moveSpeed
      this.vertices[i - 1] -= this.offset[i - 1]
      this.vertices[i + 1] -= this.offset[i + 1]

      if (this.vertices[i] < 0) {
        this.vertices[i] = 600
      }

      if (
        this.vertices[i - 1] < this.config.bounds.minX ||
        this.vertices[i - 1] > this.config.bounds.maxX
      ) {
        this.offset[i - 1] = -this.offset[i - 1]
      }

      if (
        this.vertices[i + 1] < this.config.bounds.minZ ||
        this.vertices[i + 1] > this.config.bounds.maxZ
      ) {
        this.offset[i + 1] = -this.offset[i + 1]
      }
    }

    this.geometry.setAttribute('position', new THREE.Float32BufferAttribute(this.vertices, 3))
  }

  /**
   * 动画循环
   */
  private animate = (): void => {
    if (this.state !== SnowState.RUNNING) return

    this.animationId = requestAnimationFrame(this.animate)

    if (!this.pointMesh) return

    if (this.params.snowEnabled) {
      this.pointMesh.visible = true
      this.updatePoints()

      // 后处理渲染
      viewer.scene.overrideMaterial = null
      viewer.renderer.setRenderTarget(this.postprocessing.difusse)
      viewer.renderer.render(viewer.scene, viewer.camera)

      viewer.scene.overrideMaterial = this.postprocessing.gBufferPass
      viewer.renderer.setRenderTarget(this.postprocessing.gBuffer)
      viewer.renderer.render(viewer.scene, viewer.camera)

      viewer.renderer.setRenderTarget(null)
      viewer.renderer.render(this.postprocessing.scene, this.postprocessing.camera)
    } else {
      this.pointMesh.visible = false
      viewer.scene.overrideMaterial = null
      viewer.renderer.setRenderTarget(null)
      viewer.renderer.render(viewer.scene, viewer.camera)
    }
  }

  /**
   * 启动雪花系统
   */
  public start(): void {
    if (this.state === SnowState.DESTROYED) {
      console.warn('Cannot start destroyed snow system')
      return
    }

    if (this.state === SnowState.RUNNING) {
      console.warn('Snow system is already running')
      return
    }

    this.state = SnowState.RUNNING
    this.params.snowEnabled = true

    if (this.pointMesh) {
      this.pointMesh.visible = true
    }

    this.animate()
  }

  /**
   * 停止雪花系统
   */
  public stop(): void {
    if (this.state === SnowState.DESTROYED) {
      console.warn('Cannot stop destroyed snow system')
      return
    }

    this.state = SnowState.STOPPED
    this.params.snowEnabled = false

    // 停止动画循环
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }

    // 隐藏粒子
    if (this.pointMesh) {
      this.pointMesh.visible = false
    }

    // 重置渲染器状态
    viewer.scene.overrideMaterial = null
    viewer.renderer.setRenderTarget(null)
  }

  /**
   * 销毁雪花系统，完全清理所有资源
   */
  public destroy(): void {
    this.stop()
    this.state = SnowState.DESTROYED

    // 从场景中移除粒子网格
    if (this.pointMesh) {
      viewer.scene.remove(this.pointMesh)
      this.pointMesh = null
    }

    // 清理几何体
    if (this.geometry) {
      this.geometry.dispose()
      this.geometry = null
    }

    // 清理材质
    if (this.material) {
      this.material.dispose()
      this.material = null
    }

    // 清理后处理资源
    if (this.postprocessing.difusse) {
      this.postprocessing.difusse.dispose()
    }
    if (this.postprocessing.gBuffer) {
      this.postprocessing.gBuffer.dispose()
    }
    if (this.postprocessing.gBufferPass) {
      this.postprocessing.gBufferPass.dispose()
    }
    if (this.postprocessing.finalMaterial) {
      this.postprocessing.finalMaterial.dispose()
    }
    if (this.postprocessing.quad) {
      this.postprocessing.scene.remove(this.postprocessing.quad)
    }

    // 清空数组
    this.vertices = []
    this.offset = []
  }

  /**
   * 获取当前状态
   */
  public getState(): SnowState {
    return this.state
  }

  /**
   * 检查是否正在运行
   */
  public isRunning(): boolean {
    return this.state === SnowState.RUNNING
  }

  /**
   * 检查是否已停止
   */
  public isStopped(): boolean {
    return this.state === SnowState.STOPPED
  }

  /**
   * 检查是否已销毁
   */
  public isDestroyed(): boolean {
    return this.state === SnowState.DESTROYED
  }

  /**
   * 设置雪花粒子数量（需要重新初始化才生效）
   */
  public setParticleCount(count: number): void {
    if (this.state === SnowState.RUNNING) {
      console.warn('Cannot change particle count while running. Stop first.')
      return
    }
    this.config.particleCount = count
  }

  /**
   * 设置雪花大小
   */
  public setSize(size: number): void {
    this.config.size = size
    if (this.material) {
      this.material.size = size
    }
  }

  /**
   * 设置雪花透明度
   */
  public setOpacity(opacity: number): void {
    this.config.opacity = Math.max(0, Math.min(1, opacity))
    if (this.material) {
      this.material.opacity = this.config.opacity
    }
  }

  /**
   * 设置雪花移动速度
   */
  public setMoveSpeed(speed: number): void {
    this.config.moveSpeed = speed
  }

  /**
   * 切换雪花可见性
   */
  public toggle(): void {
    if (this.isRunning()) {
      this.stop()
    } else if (this.isStopped()) {
      this.start()
    }
  }
}
