/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-11
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-13
 */
import * as THREE from 'three'
import { EXRLoader } from 'three/examples/jsm/loaders/EXRLoader.js'

export class Skybox {
  private skyBoxs: Record<string, THREE.Texture> = {}
  private scene: THREE.Scene
  private renderer: THREE.WebGLRenderer
  private visible: boolean = true
  private currentSkyBox: string | undefined

  constructor(scene: THREE.Scene, renderer: THREE.WebGLRenderer) {
    this.scene = scene
    this.renderer = renderer
  }
  private loadSkyBox(skybox: { name: string; url: string }): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      if (this.skyBoxs[skybox.name]) {
        return resolve(this.skyBoxs[skybox.name])
      }
      const pmremGenerator = new THREE.PMREMGenerator(this.renderer)
      pmremGenerator.compileEquirectangularShader()

      new EXRLoader().load(
        skybox.url,
        (texture) => {
          const envMap = pmremGenerator.fromEquirectangular(texture).texture
          this.skyBoxs[skybox.name] = envMap
          // 仅缓存环境贴图，不再自动应用到 scene.environment
          texture.dispose()
          pmremGenerator.dispose()
          resolve(envMap)
        },
        undefined,
        (error) => {
          reject(error)
        },
      )
    })
  }
  public getSkyBox(name: string): THREE.Texture {
    return this.skyBoxs[name]
  }
  public setSkyBox(skybox: { name: string; url: string }) {
    return new Promise<THREE.Texture>((resolve) => {
      if (this.currentSkyBox === skybox.name) {
        return resolve(this.skyBoxs[skybox.name])
      }
      this.loadSkyBox(skybox).then((texture) => {
        this.scene.background = texture
        // 不启用基于环境的全局光照，以避免封闭空间被“环境光”照亮
        this.currentSkyBox = skybox.name
        resolve(texture)
      })
    })
  }
  public setSkyBoxVisible(visible: boolean): void {
    if (this.visible === visible) {
      return
    }
    if (!visible) {
      this.scene.background = null
      this.scene.environment = null
    } else {
      this.scene.background = this.skyBoxs[this.currentSkyBox!]
      // 仅恢复背景，不恢复 environment
    }
    this.visible = visible
  }
}
