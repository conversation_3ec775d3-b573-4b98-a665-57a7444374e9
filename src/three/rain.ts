import * as THREE from 'three'
import { viewer } from './viewer'

// 类型定义
interface RainParams {
  autoRotate: boolean
  rainAmount: number
  bloomStrength: number
  bloomRadius: number
  bloomThreshold: number
  rippleScale: number
  rippleStrength: number
  noiseStrength: number
  lightIntensity: number
  rainOpacity: number
  rainSpeed: number
  rainDropSize: number
  rainBlurSteps: number
}

interface AnimationState {
  animateId: number | null
  updateRainFactorId: number | null
  isRunning: boolean
}

// 常量定义（参考 rain.html 的数值范围进行适配）
const MAX_PARTICLE_COUNT = 50000
const DEFAULT_RAIN_COUNT = Math.floor(MAX_PARTICLE_COUNT / 2) // 与 rain.html 对齐一半实例数量
const RAIN_SPAWN_AREA = 200 // 扩大下雨范围 [-100, 100]
const RAIN_SPAWN_HALF = RAIN_SPAWN_AREA / 2
const RAIN_TOP_Y = 60
const SURFACE_OFFSET_Y = 0.005
const FIXED_GROUND_Y = 0.5
const RIPPLE_SPEED = 4.0 // rippleTime 累加速度（与 rain.html 一致）

export class Rain {
  public readonly params: RainParams = {
    autoRotate: false,
    rainAmount: 0.0,
    bloomStrength: 0.0,
    bloomRadius: 0.0,
    bloomThreshold: 0.0,
    rippleScale: 1.0,
    rippleStrength: 1.0,
    noiseStrength: 0.0,
    lightIntensity: 1.0,
    rainOpacity: 0.2, // 与 rain.html 类似透明度
    rainSpeed: 20, // 每秒下落速度（世界单位），CPU 版本适配
    rainDropSize: 1, // 用作雨滴长度缩放参考
    rainBlurSteps: 5.0,
  }

  private animationState: AnimationState = {
    animateId: null,
    updateRainFactorId: null,
    isRunning: false,
  }

  private rainStarted = false

  // 雨滴与涟漪
  private rain: THREE.InstancedMesh | null = null
  private ripple: THREE.InstancedMesh | null = null

  // 雨滴实例数据（CPU 更新）
  private dropPositions!: Float32Array // [x,y,z] * count
  private dropScales!: Float32Array // 标量 s 存放在 y 分量中（简化），仅用于尺寸随机化
  // 为每个雨滴引入独立速度，避免同步“成波”
  private dropSpeeds!: Float32Array

  // 涟漪实例数据（GPU 读取时间，CPU 更新 attribute）
  private rippleTimes!: Float32Array // length == rippleCount，初始 1000 表示无效
  private rippleTimeAttr!: THREE.InstancedBufferAttribute
  private nextRippleIndex = 0

  private clock = new THREE.Clock()

  // 统一通过 viewer 的渲染循环更新，避免二次渲染
  private renderCallbackIndex: number | null = null

  // 与旧结构保持一致的公共控制方法（外部可直接调用）
  public start: (() => void) | null = null
  public stop: (() => void) | null = null

  // 地面高度缓存与射线检测器（用于放置涟漪）
  private heightCache = new Map<string, number>()
  private raycaster = new THREE.Raycaster()

  // 复用的临时对象，避免每帧分配造成 GC 抖动
  private readonly dummyObject: THREE.Object3D = new THREE.Object3D()

  constructor() {
    this.init()
  }

  private init(): void {
    // 仅完成必要的初始化，材质和网格在 start 时创建
    this.start = () => this.startRain()
    this.stop = () => this.stopRain()
  }

  //========== 创建材质与网格 ==========

  private createRainMaterial(): THREE.ShaderMaterial {
    const vertexShader = /* glsl */ `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * instanceMatrix * vec4( position, 1.0 );
      }
    `

    const fragmentShader = /* glsl */ `
      varying vec2 vUv;
      void main() {
        float d = distance( vUv, vec2( 0.5, 0.0 ) );
        float intensity = exp( 3.0 * ( 1.0 - d ) ) * 0.1;
        float alpha = 0.2;
        gl_FragColor = vec4( vec3( intensity ), alpha );
      }
    `

    const material = new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      transparent: true,
      depthWrite: false,
      depthTest: true,
      side: THREE.DoubleSide,
      toneMapped: false,
    })
    return material
  }

  private createRippleMaterial(): THREE.ShaderMaterial {
    // 只使用水平面涟漪，并限制颜色强度，避免刺眼白光
    const vertexShader = /* glsl */ `
      attribute float aRippleTime;
      varying float vRippleTime;
      varying vec2 vUv;
      void main() {
        vUv = uv;
        vRippleTime = aRippleTime;
        gl_Position = projectionMatrix * modelViewMatrix * instanceMatrix * vec4( position, 1.0 );
      }
    `

    const fragmentShader = /* glsl */ `
      varying float vRippleTime;
      varying vec2 vUv;
      void main() {
        vec2 c = vUv - vec2(0.5);
        float center = length(c) * 7.0;
        float distanceVal = vRippleTime - center;
        float effect = min(distanceVal, 1.0) - (max(distanceVal, 1.0) - 1.0);
        effect = clamp(effect, 0.0, 1.0);
        if (effect <= 0.001) discard;
        float life = max(0.0, 1.0 - vRippleTime * 0.3);
        float opacity = effect * life * 0.5;
        if (opacity <= 0.001) discard;
        vec3 rippleColor = vec3(1.0);
        gl_FragColor = vec4(rippleColor, opacity);
      }
    `

    const material = new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      transparent: true,
      depthWrite: false,
      depthTest: true,
      side: THREE.FrontSide, // 水平面正面朝上
      toneMapped: false,
    })
    return material
  }

  private createRainMesh(count: number): THREE.InstancedMesh {
    const geometry = new THREE.PlaneGeometry(0.1, 2.0)
    const material = this.createRainMaterial()
    const mesh = new THREE.InstancedMesh(geometry, material, count)

    // 初始化实例矩阵与 CPU 缓冲
    this.dropPositions = new Float32Array(count * 3)
    this.dropScales = new Float32Array(count)
    this.dropSpeeds = new Float32Array(count)

    const dummy = new THREE.Object3D()
    const centerX = viewer.camera.position.x
    const centerZ = viewer.camera.position.z
    for (let i = 0; i < count; i++) {
      const x = THREE.MathUtils.randFloat(centerX - RAIN_SPAWN_HALF, centerX + RAIN_SPAWN_HALF)
      const y = THREE.MathUtils.randFloat(FIXED_GROUND_Y + 5.0, RAIN_TOP_Y)
      const z = THREE.MathUtils.randFloat(centerZ - RAIN_SPAWN_HALF, centerZ + RAIN_SPAWN_HALF)
      const s = THREE.MathUtils.randFloat(0.5, 1.5) * this.params.rainDropSize

      this.dropPositions[i * 3 + 0] = x
      this.dropPositions[i * 3 + 1] = y
      this.dropPositions[i * 3 + 2] = z
      this.dropScales[i] = s
      // 速度添加轻微扰动，避免同步
      this.dropSpeeds[i] = this.params.rainSpeed * THREE.MathUtils.randFloat(0.85, 1.15)

      dummy.position.set(x, y, z)
      dummy.scale.set(1, s, 1)
      dummy.updateMatrix()
      mesh.setMatrixAt(i, dummy.matrix)
    }

    mesh.instanceMatrix.needsUpdate = true
    mesh.frustumCulled = false
    return mesh
  }

  private createRippleGeometry(): THREE.BufferGeometry {
    // 仅使用水平面的涟漪几何，避免侧面与场景几何交错导致闪烁
    const surface = new THREE.PlaneGeometry(2.5, 2.5)
    surface.rotateX(-Math.PI / 2)
    return surface
  }

  private createRippleMesh(count: number): THREE.InstancedMesh {
    const geometry = this.createRippleGeometry()
    const material = this.createRippleMaterial()
    const mesh = new THREE.InstancedMesh(geometry, material, count)

    // 初始化 aRippleTime attributes，初始无效（1000）
    this.rippleTimes = new Float32Array(count)
    this.rippleTimes.fill(1000)
    this.rippleTimeAttr = new THREE.InstancedBufferAttribute(this.rippleTimes, 1)
    geometry.setAttribute('aRippleTime', this.rippleTimeAttr)

    const dummy = new THREE.Object3D()
    for (let i = 0; i < count; i++) {
      dummy.position.set(0, -9999, 0) // 初始放到看不到的位置
      dummy.updateMatrix()
      mesh.setMatrixAt(i, dummy.matrix)
    }

    mesh.instanceMatrix.needsUpdate = true
    mesh.frustumCulled = false
    return mesh
  }

  //========== 更新逻辑 ==========

  private update = (): void => {
    if (!this.animationState.isRunning) return
    const delta = this.clock.getDelta()

    const effectiveCount = Math.floor(
      DEFAULT_RAIN_COUNT * THREE.MathUtils.clamp(this.params.rainAmount, 0, 1),
    )

    if (this.rain) {
      const dummy = this.dummyObject
      const camPos = viewer.camera.position

      for (let i = 0; i < effectiveCount; i++) {
        const idx3 = i * 3
        let x = this.dropPositions[idx3 + 0]
        let y = this.dropPositions[idx3 + 1]
        let z = this.dropPositions[idx3 + 2]
        const s = this.dropScales[i]

        // 使用每滴独立速度，避免同步
        y -= this.dropSpeeds[i] * delta

        if (y <= FIXED_GROUND_Y) {
          this.spawnRippleAt(x, z)
          const camX = camPos.x
          const camZ = camPos.z
          x = THREE.MathUtils.randFloat(camX - RAIN_SPAWN_HALF, camX + RAIN_SPAWN_HALF)
          // 重生高度加入抖动，进一步打散相位
          y = THREE.MathUtils.randFloat(RAIN_TOP_Y - 5.0, RAIN_TOP_Y + 5.0)
          z = THREE.MathUtils.randFloat(camZ - RAIN_SPAWN_HALF, camZ + RAIN_SPAWN_HALF)
          // 重新分配轻微不同的速度
          this.dropSpeeds[i] = this.params.rainSpeed * THREE.MathUtils.randFloat(0.85, 1.15)
        }

        dummy.position.set(x, y, z)
        dummy.rotation.y = Math.atan2(camPos.x - x, camPos.z - z)
        dummy.scale.set(1, s, 1)
        dummy.updateMatrix()
        this.rain.setMatrixAt(i, dummy.matrix)

        this.dropPositions[idx3 + 0] = x
        this.dropPositions[idx3 + 1] = y
        this.dropPositions[idx3 + 2] = z
      }

      for (let i = effectiveCount; i < this.rain.count; i++) {
        const dummy = this.dummyObject
        dummy.position.set(0, -9999, 0)
        dummy.updateMatrix()
        this.rain.setMatrixAt(i, dummy.matrix)
      }

      this.rain.instanceMatrix.needsUpdate = true
    }

    if (this.ripple) {
      let anyChange = false
      for (let i = 0; i < this.ripple.count; i++) {
        if (this.rippleTimes[i] < 1000) {
          this.rippleTimes[i] += delta * RIPPLE_SPEED
          if (this.rippleTimes[i] > 5.0) {
            this.rippleTimes[i] = 1000
          }
          anyChange = true
        }
      }
      if (anyChange) {
        this.rippleTimeAttr.needsUpdate = true
      }
    }
  }

  private spawnRippleAt(x: number, z: number): void {
    if (!this.ripple) return

    const i = this.nextRippleIndex
    this.nextRippleIndex = (this.nextRippleIndex + 1) % this.ripple.count

    const dummy = this.dummyObject
    dummy.position.set(x, FIXED_GROUND_Y, z)
    dummy.updateMatrix()
    this.ripple.setMatrixAt(i, dummy.matrix)

    this.rippleTimes[i] = 0.0
    this.rippleTimeAttr.needsUpdate = true
    this.ripple.instanceMatrix.needsUpdate = true
  }

  //========== 生命周期控制 ==========

  private startRain(): void {
    if (this.rainStarted) return

    const count = DEFAULT_RAIN_COUNT
    this.rain = this.createRainMesh(count)
    this.ripple = this.createRippleMesh(count)
    viewer.scene.add(this.rain)
    viewer.scene.add(this.ripple)

    // viewer.skybox.setSkyBoxVisible(false)
    viewer.skybox.setSkyBox({
      name: 'cloudy',
      url: 'skybox/skybox-rain.exr',
    })
    const hour = new Date().getHours()
    const isNight = (hour >= 19 && hour <= 24) || (hour >= 0 && hour <= 7)
    viewer.renderer.toneMappingExposure = isNight ? 0.1 : 0.3
    this.animationState.isRunning = true
    if (this.renderCallbackIndex === null) {
      this.renderCallbackIndex = viewer.addRenderCallback(this.update)
    }

    this.fadeInRain()
    this.rainStarted = true
  }

  private fadeInRain(): void {
    const startTime = performance.now()
    const duration = 5000

    const step = (now: number) => {
      const t = Math.min(1, (now - startTime) / duration)
      this.params.rainAmount = t
      if (t < 1) {
        this.animationState.updateRainFactorId = requestAnimationFrame(step)
      } else {
        this.animationState.updateRainFactorId = null
      }
    }

    this.animationState.updateRainFactorId = requestAnimationFrame(step)
  }

  private stopRain(): void {
    this.animationState.isRunning = false
    if (this.animationState.updateRainFactorId) {
      cancelAnimationFrame(this.animationState.updateRainFactorId)
      this.animationState.updateRainFactorId = null
    }

    // viewer.skybox.setSkyBoxVisible(true)
    const hour = new Date().getHours()
    const isNight = (hour >= 19 && hour <= 24) || (hour >= 0 && hour <= 7)
    const sb = isNight
      ? {
          name: 'night',
          url: 'skybox/skybox-night.exr',
        }
      : {
          name: 'day',
          url: 'skybox/skybox-day.exr',
        }
    viewer.skybox.setSkyBox(sb)

    viewer.renderer.toneMappingExposure = isNight ? 0.2 : 1

    if (this.renderCallbackIndex !== null) {
      viewer.removeRenderCallback(this.renderCallbackIndex)
      this.renderCallbackIndex = null
    }

    if (this.rain) {
      viewer.scene.remove(this.rain)
      this.rain.geometry.dispose()
      ;(Array.isArray(this.rain.material) ? this.rain.material : [this.rain.material]).forEach(
        (m) => m.dispose(),
      )
      this.rain.clear()
      this.rain = null
    }

    if (this.ripple) {
      viewer.scene.remove(this.ripple)
      this.ripple.geometry.dispose()
      ;(Array.isArray(this.ripple.material)
        ? this.ripple.material
        : [this.ripple.material]
      ).forEach((m) => m.dispose())
      this.ripple.clear()
      this.ripple = null
    }

    this.params.rainAmount = 0.0
    this.rainStarted = false
  }

  //========== 兼容原有对外 API ==========

  public removeGroundPlane(): void {}

  public getGroundPlane(): THREE.Mesh | null {
    return null
  }

  public getGroundPlaneInfo(): null {
    return null
  }

  public isRainStarted(): boolean {
    return this.rainStarted
  }

  public getRainObject(): THREE.InstancedMesh | null {
    return this.rain
  }

  public isRainInScene(): boolean {
    if (!this.rain) return false
    return viewer.scene.children.includes(this.rain)
  }

  public getAnimationState(): AnimationState {
    return { ...this.animationState }
  }

  public getAudioState(): { isPlaying: boolean; volumes: Record<string, number> } | null {
    return { isPlaying: this.rainStarted, volumes: {} }
  }

  public updateParams(newParams: Partial<RainParams>): void {
    Object.assign(this.params, newParams)
  }

  public dispose(): void {
    try {
      if (this.rainStarted) this.stopRain()
      this.rain = null
      this.ripple = null
      this.animationState = { animateId: null, updateRainFactorId: null, isRunning: false }
    } catch (e) {
      console.error('销毁 Rain 实例失败:', e)
    }
  }
}
