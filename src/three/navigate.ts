/*
 * @Description: 导航模块，包含定位到指定模型、指定坐标、多点定位等功能
 * @Author: AI Assistant
 * @Date: 2023-07-22
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-22
 */

import * as THREE from 'three'
import { viewer } from './viewer'
import { threeJSEvents } from './evens'
import * as TWEEN from '@tweenjs/tween.js'

/**
 * 相机动画配置
 */
interface CameraAnimationOptions {
  /** 动画持续时间（毫秒） */
  duration?: number
  /** 动画缓动函数 */
  easing?: (k: number) => number
  /** 动画完成回调 */
  onComplete?: () => void
  /** 动画更新回调 */
  onUpdate?: (position: THREE.Vector3, target: THREE.Vector3) => void
}

/**
 * 位置点定义
 */
export interface PositionPoint {
  /** 位置名称 */
  name: string
  /** 相机位置 */
  position: [number, number, number]
  /** 目标位置（相机看向的点） */
  target: [number, number, number]
}

/**
 * 导航管理类
 */
class NavigateManager {
  /** 当前相机动画 */
  private currentAnimation: TWEEN.Tween | null = null
  /** 位置点列表 */
  private positionPoints: Map<string, PositionPoint> = new Map()
  /** 巡航路径 */
  private tourPath: string[] = []
  /** 巡航索引 */
  private tourIndex = 0
  /** 是否正在巡航 */
  private isTourActive = false
  /** 巡航间隔（毫秒） */
  private tourInterval = 5000
  /** 巡航定时器 */
  private tourTimer: number | null = null
  /** 动画帧ID */
  private animationFrameId: number | null = null
  /** 是否已经初始化了动画循环 */
  private animationLoopInitialized = false

  constructor() {
    // 初始化动画更新
    this.setupAnimationLoop()
  }

  /**
   * 设置动画循环
   */
  private setupAnimationLoop(): void {
    if (this.animationLoopInitialized) {
      return
    }

    // 确保TWEEN动画能够正常更新
    const updateTween = () => {
      this.animationFrameId = requestAnimationFrame(updateTween)
      TWEEN.update()
    }

    // 启动动画循环
    this.animationFrameId = requestAnimationFrame(updateTween)
    this.animationLoopInitialized = true

    // 监听场景更新事件（作为备用，确保多重保险）
    threeJSEvents.subscribe('scene:update', () => {
      // 在场景更新时也更新TWEEN
      TWEEN.update()
    })
  }

  /**
   * 定位到指定模型
   * @param object 目标模型对象
   * @param options 相机动画选项
   */
  public flyToObject(object: THREE.Object3D, options?: CameraAnimationOptions): void {
    if (!viewer) {
      return
    }

    // 计算模型包围盒
    const boundingBox = new THREE.Box3().setFromObject(object)

    // 检查包围盒是否有效
    if (boundingBox.isEmpty()) {
      return
    }

    const center = new THREE.Vector3()
    boundingBox.getCenter(center)

    // 计算包围盒尺寸和适当的相机距离
    const size = new THREE.Vector3()
    boundingBox.getSize(size)
    const maxDim = Math.max(size.x, size.y, size.z)
    const distance = maxDim * 1 // 距离是最大尺寸的2倍，可以根据需要调整

    // 计算相机位置：从中心点向上和向后偏移
    const direction = new THREE.Vector3(1, 1, 1).normalize()
    const position = center.clone().add(direction.multiplyScalar(distance))

    // 飞行到计算出的位置，并看向模型中心
    this.flyTo(position, center, options)
  }

  /**
   * 定位到指定坐标
   * @param position 目标位置
   * @param target 目标朝向（可选，默认为场景中心）
   * @param options 相机动画选项
   */
  public flyTo(
    position: THREE.Vector3 | [number, number, number],
    target?: THREE.Vector3 | [number, number, number],
    options?: CameraAnimationOptions,
  ): void {
    if (!viewer) {
      return
    }

    // 确保动画循环已初始化
    this.setupAnimationLoop()

    // 取消当前动画
    if (this.currentAnimation) {
      this.currentAnimation.stop()
      this.currentAnimation = null
    }

    const camera = viewer.getCamera()
    const controls = viewer.getControls()

    if (!camera || !controls) {
      return
    }

    // 转换位置参数为Vector3对象
    const targetPosition =
      position instanceof THREE.Vector3 ? position : new THREE.Vector3(...position)

    // 确定目标朝向
    const targetLookAt = target
      ? target instanceof THREE.Vector3
        ? target
        : new THREE.Vector3(...target)
      : new THREE.Vector3(0, 0, 0) // 默认看向原点

    // 设置默认选项
    const defaultOptions: Required<CameraAnimationOptions> = {
      duration: 1000,
      easing: TWEEN.Easing.Cubic.InOut,
      onComplete: () => {},
      onUpdate: () => {},
    }
    const finalOptions = { ...defaultOptions, ...options }

    // 创建当前相机状态对象
    const from = {
      posX: camera.position.x,
      posY: camera.position.y,
      posZ: camera.position.z,
      targetX: controls.target.x,
      targetY: controls.target.y,
      targetZ: controls.target.z,
    }

    // 创建目标相机状态对象
    const to = {
      posX: targetPosition.x,
      posY: targetPosition.y,
      posZ: targetPosition.z,
      targetX: targetLookAt.x,
      targetY: targetLookAt.y,
      targetZ: targetLookAt.z,
    }

    // 直接设置相机位置的函数
    const directUpdate = () => {
      camera.position.set(targetPosition.x, targetPosition.y, targetPosition.z)
      controls.target.set(targetLookAt.x, targetLookAt.y, targetLookAt.z)
      controls.update()
    }

    // 如果动画时长为0，直接更新到目标位置
    if (finalOptions.duration <= 0) {
      directUpdate()
      finalOptions.onComplete()
      return
    }

    try {
      // 使用新的TWEEN Group确保动画独立更新
      const tweenGroup = new TWEEN.Group()

      this.currentAnimation = new TWEEN.Tween(from, tweenGroup)
        .to(to, finalOptions.duration)
        .easing(finalOptions.easing)
        .onUpdate(() => {
          // 更新相机位置
          camera.position.set(from.posX, from.posY, from.posZ)

          // 更新控制器目标点
          controls.target.set(from.targetX, from.targetY, from.targetZ)
          controls.update()

          // 调用更新回调
          const currentPos = new THREE.Vector3(from.posX, from.posY, from.posZ)
          const currentTarget = new THREE.Vector3(from.targetX, from.targetY, from.targetZ)
          finalOptions.onUpdate(currentPos, currentTarget)

          // 发布相机移动事件
          threeJSEvents.publish('camera:move', { position: camera.position })
        })
        .onComplete(() => {
          this.currentAnimation = null

          // 确保最终位置正确
          camera.position.set(targetPosition.x, targetPosition.y, targetPosition.z)
          controls.target.set(targetLookAt.x, targetLookAt.y, targetLookAt.z)
          controls.update()

          finalOptions.onComplete()
        })

      // 启动动画
      this.currentAnimation.start()

      // 设置一个独立的动画更新循环，确保这个特定动画能够更新
      const updateThisAnimation = () => {
        if (this.currentAnimation) {
          tweenGroup.update()
          requestAnimationFrame(updateThisAnimation)
        }
      }
      requestAnimationFrame(updateThisAnimation)
    } catch (error) {
      // 如果动画创建失败，直接设置相机位置
      directUpdate()
    }
  }

  /**
   * 添加位置点
   * @param id 位置点ID
   * @param point 位置点数据
   */
  public addPositionPoint(id: string, point: PositionPoint): void {
    this.positionPoints.set(id, point)
  }

  /**
   * 删除位置点
   * @param id 位置点ID
   */
  public removePositionPoint(id: string): boolean {
    return this.positionPoints.delete(id)
  }

  /**
   * 获取位置点
   * @param id 位置点ID
   */
  public getPositionPoint(id: string): PositionPoint | undefined {
    return this.positionPoints.get(id)
  }

  /**
   * 获取所有位置点
   */
  public getAllPositionPoints(): Map<string, PositionPoint> {
    return this.positionPoints
  }

  /**
   * 定位到预设位置点
   * @param id 位置点ID
   * @param options 相机动画选项
   */
  public flyToPositionPoint(id: string, options?: CameraAnimationOptions): void {
    const point = this.positionPoints.get(id)
    if (!point) {
      console.warn(`位置点 "${id}" 不存在`)
      return
    }

    this.flyTo(point.position, point.target, options)
  }

  /**
   * 设置巡航路径
   * @param pointIds 位置点ID数组
   */
  public setTourPath(pointIds: string[]): void {
    // 验证所有点是否存在
    const validIds = pointIds.filter((id) => this.positionPoints.has(id))
    if (validIds.length !== pointIds.length) {
      console.warn('部分位置点不存在，已被过滤')
    }

    this.tourPath = validIds
    this.tourIndex = 0
  }

  /**
   * 开始巡航
   * @param interval 巡航间隔（毫秒）
   */
  public startTour(interval: number = 5000): void {
    if (this.tourPath.length === 0) {
      console.warn('巡航路径为空，无法开始巡航')
      return
    }

    if (this.isTourActive) {
      this.stopTour()
    }

    this.tourInterval = interval
    this.isTourActive = true
    this.tourIndex = 0

    // 立即前往第一个点
    this.moveToNextTourPoint()
  }

  /**
   * 停止巡航
   */
  public stopTour(): void {
    this.isTourActive = false
    if (this.tourTimer !== null) {
      window.clearTimeout(this.tourTimer)
      this.tourTimer = null
    }
  }

  /**
   * 定位到指定坐标并设置相机旋转
   * @param position 目标位置
   * @param rotation 相机旋转角度（欧拉角，弧度制）
   * @param options 相机动画选项
   */
  public flyToWithRotation(
    position: THREE.Vector3 | [number, number, number],
    rotation: THREE.Euler | [number, number, number],
    options?: CameraAnimationOptions,
  ): void {
    if (!viewer) {
      return
    }

    // 确保动画循环已初始化
    this.setupAnimationLoop()

    // 取消当前动画
    if (this.currentAnimation) {
      this.currentAnimation.stop()
      this.currentAnimation = null
    }

    const camera = viewer.getCamera()
    const controls = viewer.getControls()

    if (!camera || !controls) {
      return
    }

    // 转换位置参数为Vector3对象
    const targetPosition =
      position instanceof THREE.Vector3 ? position : new THREE.Vector3(...position)

    // 转换旋转参数为Euler对象
    const targetRotation =
      rotation instanceof THREE.Euler
        ? rotation
        : new THREE.Euler(rotation[0], rotation[1], rotation[2])

    // 计算相机前方向量
    const direction = new THREE.Vector3(0, 0, -1).applyEuler(targetRotation)

    // 计算目标点 - 在相机前方较远的点（增加距离系数）
    const lookAtDistance = 50 // 增加目标点与相机的距离
    const targetLookAt = targetPosition.clone().add(direction.multiplyScalar(lookAtDistance))

    // 设置默认选项
    const defaultOptions: Required<CameraAnimationOptions> = {
      duration: 1000,
      easing: TWEEN.Easing.Cubic.InOut,
      onComplete: () => {},
      onUpdate: () => {},
    }
    const finalOptions = { ...defaultOptions, ...options }

    // 保存控制器状态
    const wasEnabled = controls.enabled

    // 暂时禁用控制器，以便我们可以直接控制相机
    controls.enabled = false

    // 创建当前相机状态对象
    const from = {
      posX: camera.position.x,
      posY: camera.position.y,
      posZ: camera.position.z,
      // 我们将使用目标点而不是直接设置旋转
      targetX: controls.target.x,
      targetY: controls.target.y,
      targetZ: controls.target.z,
    }

    // 创建目标相机状态对象
    const to = {
      posX: targetPosition.x,
      posY: targetPosition.y,
      posZ: targetPosition.z,
      targetX: targetLookAt.x,
      targetY: targetLookAt.y,
      targetZ: targetLookAt.z,
    }

    // 直接设置相机位置和目标的函数
    const directUpdate = () => {
      camera.position.set(targetPosition.x, targetPosition.y, targetPosition.z)
      controls.target.set(targetLookAt.x, targetLookAt.y, targetLookAt.z)
      controls.update()
    }

    // 如果动画时长为0，直接更新到目标位置
    if (finalOptions.duration <= 0) {
      directUpdate()
      controls.enabled = wasEnabled // 恢复控制器状态
      finalOptions.onComplete()
      return
    }

    try {
      // 使用新的TWEEN Group确保动画独立更新
      const tweenGroup = new TWEEN.Group()

      this.currentAnimation = new TWEEN.Tween(from, tweenGroup)
        .to(to, finalOptions.duration)
        .easing(finalOptions.easing)
        .onUpdate(() => {
          // 更新相机位置
          camera.position.set(from.posX, from.posY, from.posZ)

          // 更新控制器目标点，这将间接设置相机的朝向
          controls.target.set(from.targetX, from.targetY, from.targetZ)
          controls.update()

          // 调用更新回调
          const currentPos = new THREE.Vector3(from.posX, from.posY, from.posZ)
          const currentTarget = new THREE.Vector3(from.targetX, from.targetY, from.targetZ)
          finalOptions.onUpdate(currentPos, currentTarget)

          // 发布相机移动事件
          threeJSEvents.publish('camera:move', { position: camera.position })
        })
        .onComplete(() => {
          this.currentAnimation = null

          // 确保最终位置和目标正确
          camera.position.set(targetPosition.x, targetPosition.y, targetPosition.z)
          controls.target.set(targetLookAt.x, targetLookAt.y, targetLookAt.z)
          controls.update()

          // 恢复控制器状态
          controls.enabled = wasEnabled

          finalOptions.onComplete()
        })

      // 启动动画
      this.currentAnimation.start()

      // 设置一个独立的动画更新循环，确保这个特定动画能够更新
      const updateThisAnimation = () => {
        if (this.currentAnimation) {
          tweenGroup.update()
          requestAnimationFrame(updateThisAnimation)
        }
      }
      requestAnimationFrame(updateThisAnimation)
    } catch (error) {
      // 如果动画创建失败，直接设置相机位置和目标
      directUpdate()
      // 恢复控制器状态
      controls.enabled = wasEnabled
    }
  }

  /**
   * 移动到下一个巡航点
   */
  private moveToNextTourPoint(): void {
    if (!this.isTourActive || this.tourPath.length === 0) return

    const currentPointId = this.tourPath[this.tourIndex]

    // 定位到当前点
    this.flyToPositionPoint(currentPointId, {
      onComplete: () => {
        // 设置下一个点的定时器
        if (this.isTourActive) {
          this.tourTimer = window.setTimeout(() => {
            // 更新索引到下一个点
            this.tourIndex = (this.tourIndex + 1) % this.tourPath.length
            this.moveToNextTourPoint()
          }, this.tourInterval)
        }
      },
    })
  }
}

// 创建单例实例
export const navigate = new NavigateManager()
