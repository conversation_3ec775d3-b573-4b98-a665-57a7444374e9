/*
 * @Description: 场景对象高亮管理模块
 * @Author: AI Assistant
 * @Date: 2023-07-10
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-08
 */

import * as THREE from 'three'
import { viewer } from './viewer'
import { threeJSEvents } from './evens'

interface HighlightOptions {
  color?: THREE.Color | string | number
  edgeStrength?: number
  edgeGlow?: number
  edgeThickness?: number
  pulsePeriod?: number
  visibleEdgeColor?: THREE.Color | string | number
  hiddenEdgeColor?: THREE.Color | string | number
  renderToScreen?: boolean // 是否直接渲染到屏幕
  preserveBrightness?: boolean // 是否保持原始亮度
}

class HighlightManager {
  private static instance: HighlightManager
  private outlinePass: any // 轮廓渲染通道
  private composer: any // 效果合成器
  private highlightedObjects: Map<string, THREE.Object3D> = new Map()
  private defaultOptions: HighlightOptions = {
    color: 0x00ffff, // 默认高亮颜色（青色）
    edgeStrength: 5, // 边缘强度，降低以减少对场景亮度的影响
    edgeGlow: 0.75, // 发光强度，降低以减少对场景亮度的影响
    edgeThickness: 1, // 边缘厚度
    pulsePeriod: 0, // 脉冲周期（0表示不脉冲）
    visibleEdgeColor: 0x00ffff, // 可见边缘颜色
    hiddenEdgeColor: 0x00ffff, // 被遮挡的边缘颜色
    renderToScreen: true, // 默认直接渲染到屏幕
    preserveBrightness: true, // 默认保持原始亮度
  }

  constructor() {}

  public init(): void {
    this.initOutlineEffect()
    this.setupEventListeners()
  }

  public static getInstance(): HighlightManager {
    if (!HighlightManager.instance) {
      HighlightManager.instance = new HighlightManager()
    }
    return HighlightManager.instance
  }

  /**
   * 初始化轮廓效果
   */
  private async initOutlineEffect(): Promise<void> {
    // 动态导入 OutlinePass 和 EffectComposer
    const { EffectComposer } = await import('three/addons/postprocessing/EffectComposer.js')
    const { RenderPass } = await import('three/addons/postprocessing/RenderPass.js')
    const { OutlinePass } = await import('three/addons/postprocessing/OutlinePass.js')
    const { ShaderPass } = await import('three/addons/postprocessing/ShaderPass.js')
    const { FXAAShader } = await import('three/addons/shaders/FXAAShader.js')
    const { OutputPass } = await import('three/addons/postprocessing/OutputPass.js')

    if (!viewer) return

    const renderer = viewer.getRenderer()
    const scene = viewer.getScene()
    const camera = viewer.getCamera()

    // 保存原始的渲染尺寸
    const originalSize = new THREE.Vector2()
    renderer.getSize(originalSize)

    // 创建效果合成器
    this.composer = new EffectComposer(renderer)
    this.composer.setSize(originalSize.x, originalSize.y)

    // 添加渲染通道
    const renderPass = new RenderPass(scene, camera)
    renderPass.clearColor = new THREE.Color(0, 0, 0)
    renderPass.clearAlpha = 0
    this.composer.addPass(renderPass)

    // 创建轮廓通道
    const resolution = new THREE.Vector2(originalSize.x, originalSize.y)
    this.outlinePass = new OutlinePass(resolution, scene, camera)

    // 设置默认轮廓参数
    this.setHighlightOptions(this.defaultOptions)

    // 轮廓通道不渲染到屏幕
    this.outlinePass.renderToScreen = false
    this.composer.addPass(this.outlinePass)

    // 添加FXAA抗锯齿通道
    const fxaaPass = new ShaderPass(FXAAShader)
    fxaaPass.uniforms['resolution'].value.set(1 / originalSize.x, 1 / originalSize.y)
    fxaaPass.renderToScreen = false
    this.composer.addPass(fxaaPass)

    // 添加输出通道，确保正确的色彩和亮度
    const outputPass = new OutputPass()
    outputPass.renderToScreen = true
    this.composer.addPass(outputPass)
    viewer.composers.push(this.composer)

    // 保存原始的animate方法
    const originalAnimate = viewer.animate

    // 创建一个新的渲染循环
    // viewer.animate = () => {
    //   requestAnimationFrame(originalAnimate)
    //   this.composer.render()
    // }

    // viewer.addRenderCallback(() => {
    //   this.composer.render()
    // })

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      const width = window.innerWidth
      const height = window.innerHeight

      // 更新合成器尺寸
      this.composer.setSize(width, height)

      // 更新轮廓通道分辨率
      this.outlinePass.resolution.set(width, height)

      // 更新FXAA通道分辨率
      fxaaPass.uniforms['resolution'].value.set(1 / width, 1 / height)
    })
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听对象点击事件，可以在这里添加点击高亮功能
    // threeJSEvents.subscribe('object:dblclick', (data) => {
    //   // 取消上一个高亮
    //   this.clearAllHighlights()
    //   // 这里可以实现点击高亮功能，根据需要取消注释
    //   this.highlightObject(data.object)
    // })
  }

  /**
   * 设置高亮选项
   * @param options 高亮选项
   */
  public setHighlightOptions(options: HighlightOptions): void {
    if (!this.outlinePass) return

    if (options.color !== undefined) {
      this.outlinePass.visibleEdgeColor.set(options.color)
      this.outlinePass.hiddenEdgeColor.set(options.color)
    }

    if (options.edgeStrength !== undefined) {
      this.outlinePass.edgeStrength = options.edgeStrength
    }

    if (options.edgeGlow !== undefined) {
      this.outlinePass.edgeGlow = options.edgeGlow
    }

    if (options.edgeThickness !== undefined) {
      this.outlinePass.edgeThickness = options.edgeThickness
    }

    if (options.pulsePeriod !== undefined) {
      this.outlinePass.pulsePeriod = options.pulsePeriod
    }

    if (options.visibleEdgeColor !== undefined) {
      this.outlinePass.visibleEdgeColor.set(options.visibleEdgeColor)
    }

    if (options.hiddenEdgeColor !== undefined) {
      this.outlinePass.hiddenEdgeColor.set(options.hiddenEdgeColor)
    }

    if (options.renderToScreen !== undefined) {
      this.outlinePass.renderToScreen = options.renderToScreen
    }

    if (options.preserveBrightness !== undefined) {
      this.outlinePass.preserveBrightness = options.preserveBrightness
    }
  }

  /**
   * 高亮指定对象
   * @param object 要高亮的对象
   * @param options 高亮选项
   */
  public highlightObject(object: THREE.Object3D, options?: HighlightOptions): void {
    if (!this.outlinePass || !object) return

    // 如果提供了新的选项，则应用这些选项
    if (options) {
      this.setHighlightOptions(options)
    }

    // 保存对象引用，以便后续可以清除高亮
    const objectId = object.uuid
    this.highlightedObjects.set(objectId, object)

    // 更新高亮对象列表
    this.updateHighlightedObjects()
  }

  /**
   * 高亮多个对象
   * @param objects 要高亮的对象数组
   * @param options 高亮选项
   */
  public highlightObjects(objects: THREE.Object3D[], options?: HighlightOptions): void {
    if (!this.outlinePass || !objects.length) return

    // 如果提供了新的选项，则应用这些选项
    if (options) {
      this.setHighlightOptions(options)
    }

    // 保存对象引用
    objects.forEach((object) => {
      if (object) {
        this.highlightedObjects.set(object.uuid, object)
      }
    })

    // 更新高亮对象列表
    this.updateHighlightedObjects()
  }

  /**
   * 通过对象ID高亮对象
   * @param objectId 对象的UUID
   * @param options 高亮选项
   */
  public highlightObjectById(objectId: string, options?: HighlightOptions): boolean {
    if (!viewer) return false

    const object = this.findObjectById(viewer.scene, objectId)
    if (object) {
      this.highlightObject(object, options)
      return true
    }
    return false
  }

  /**
   * 清除指定对象的高亮
   * @param object 要清除高亮的对象
   */
  public clearObjectHighlight(object: THREE.Object3D): void {
    if (!this.outlinePass || !object) return

    // 从高亮对象列表中移除
    this.highlightedObjects.delete(object.uuid)

    // 更新高亮对象列表
    this.updateHighlightedObjects()
  }

  /**
   * 通过对象ID清除高亮
   * @param objectId 对象的UUID
   */
  public clearObjectHighlightById(objectId: string): boolean {
    const object = this.highlightedObjects.get(objectId)
    if (object) {
      this.clearObjectHighlight(object)
      return true
    }
    return false
  }

  /**
   * 清除所有高亮
   */
  public clearAllHighlights(): void {
    if (!this.outlinePass) return

    // 清空高亮对象列表
    this.highlightedObjects.clear()

    // 更新高亮对象列表（传入空数组）
    this.outlinePass.selectedObjects = []
  }

  /**
   * 更新高亮对象列表
   */
  private updateHighlightedObjects(): void {
    if (!this.outlinePass) return

    // 将Map中的值转换为数组
    const objectsArray = Array.from(this.highlightedObjects.values())

    // 更新轮廓通道的选中对象
    this.outlinePass.selectedObjects = objectsArray
  }

  /**
   * 递归查找指定ID的对象
   * @param parent 父对象
   * @param id 对象ID
   * @returns 找到的对象或undefined
   */
  private findObjectById(parent: THREE.Object3D, id: string): THREE.Object3D | undefined {
    if (parent.uuid === id) {
      return parent
    }

    for (const child of parent.children) {
      const found = this.findObjectById(child, id)
      if (found) return found
    }

    return undefined
  }

  /**
   * 设置高亮颜色
   * @param color 颜色（可以是十六进制数字、字符串或THREE.Color）
   */
  public setHighlightColor(color: THREE.Color | string | number): void {
    this.setHighlightOptions({ color })
  }

  /**
   * 调整轮廓效果强度
   * @param strength 边缘强度 (0-10)
   * @param glow 发光强度 (0-5)
   * @param thickness 边缘厚度 (1-5)
   */
  public adjustOutlineIntensity(strength?: number, glow?: number, thickness?: number): void {
    const options: HighlightOptions = {}

    if (strength !== undefined) {
      options.edgeStrength = Math.max(0, Math.min(10, strength))
    }

    if (glow !== undefined) {
      options.edgeGlow = Math.max(0, Math.min(5, glow))
    }

    if (thickness !== undefined) {
      options.edgeThickness = Math.max(1, Math.min(5, thickness))
    }

    this.setHighlightOptions(options)
  }
}

// 导出单例实例
export const highlightManager = new HighlightManager()

export const initHighlightManager = () => {
  highlightManager.init()
}

// 导出默认实例和类型
export default highlightManager
export type { HighlightOptions }
