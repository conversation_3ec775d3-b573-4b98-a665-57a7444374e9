/*
 * @Description: 标签管理模块
 * @Author: AI
 * @Date: 2023-07-12
 * @LastEditors: 余承
 * @LastEditTime: 2025-06-27
 */

import * as THREE from 'three'
import { CSS2DRenderer, CSS2DObject } from 'three/addons/renderers/CSS2DRenderer.js'
import { viewer } from './viewer'
import { threeJSEvents } from './evens'

/**
 * 标签样式配置接口
 */
interface LabelStyle {
  className?: string
  backgroundColor?: string
  color?: string
  padding?: string
  borderRadius?: string
  fontSize?: string
  fontWeight?: string
  boxShadow?: string
  border?: string
  pointerEvents?: string
  userSelect?: string
  opacity?: string
  transform?: string
  maxWidth?: string
  overflow?: string
  textOverflow?: string
  whiteSpace?: string
  cursor?: string
}

/**
 * 图片配置接口
 */
interface ImageOptions {
  src: string
  width?: string
  height?: string
  alt?: string
}

/**
 * 标签配置接口
 */
interface LabelOptions {
  id?: string
  position: THREE.Vector3 | [number, number, number]
  text?: string | HTMLElement
  image?: ImageOptions
  style?: LabelStyle
  visible?: boolean
  tag?: string
  group?: string
  onPointerDown?: (event: MouseEvent) => void
  onPointerUp?: (event: MouseEvent) => void
  onPointerEnter?: (event: MouseEvent) => void
  onPointerLeave?: (event: MouseEvent) => void
  onClick?: (event: MouseEvent) => void
}

export interface Label extends CSS2DObject {
  tag?: string
  group?: string
}

/**
 * 标签管理器类
 */
class LabelManager {
  private static instance: LabelManager
  private css2DRenderer: CSS2DRenderer
  private labels: Map<string, Label> = new Map()
  private container: HTMLDivElement | null = null
  private isInitialized = false

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {
    this.css2DRenderer = new CSS2DRenderer()
    this.css2DRenderer.domElement.style.position = 'absolute'
    this.css2DRenderer.domElement.style.top = '0'
    this.css2DRenderer.domElement.style.left = '0'
    this.css2DRenderer.domElement.style.pointerEvents = 'none'
    this.css2DRenderer.domElement.style.zIndex = '1000' // 确保标签显示在最上层
    this.css2DRenderer.setSize(1920, 1080)
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LabelManager {
    if (!LabelManager.instance) {
      LabelManager.instance = new LabelManager()
    }
    return LabelManager.instance
  }

  /**
   * 初始化标签管理器
   * @param container 容器元素
   */
  public initialize(container: HTMLDivElement): void {
    if (this.isInitialized) {
      return
    }
    this.container = container
    this.css2DRenderer.setSize(container.clientWidth, container.clientHeight)
    container.appendChild(this.css2DRenderer.domElement)

    // 添加到渲染循环
    this.setupRenderLoop()

    // 监听窗口大小变化
    this.setupEventListeners()

    this.isInitialized = true
  }

  /**
   * 设置渲染循环
   */
  private setupRenderLoop(): void {
    if (!viewer) {
      console.error('Viewer未初始化，无法设置渲染循环')
      return
    }
    const animate = () => {
      requestAnimationFrame(animate)
      this.css2DRenderer.render(viewer.scene, viewer.camera)
    }
    animate()
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听窗口大小变化
    threeJSEvents.subscribe('scene:resize', (data: { width: number; height: number }) => {
      if (this.css2DRenderer) {
        this.css2DRenderer.setSize(data.width, data.height)
      }
    })
  }

  /**
   * 创建默认样式
   */
  private createDefaultStyle(): LabelStyle {
    return {
      color: '#ffffff',
      fontSize: '14px',
      fontWeight: '400',
      pointerEvents: 'auto',
      userSelect: 'none',
      maxWidth: '200px',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
    }
  }

  /**
   * 应用样式到元素
   * @param element HTML元素
   * @param style 样式配置
   */
  private applyStyle(element: HTMLElement, style: LabelStyle): void {
    Object.entries(style).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        ;(element.style as any)[key] = value
      }
    })
  }

  /**
   * 创建标签元素
   * @param options 标签配置
   * @returns 标签对象
   */
  public createLabel(options: LabelOptions): Label {
    if (!viewer || !viewer.scene) {
      console.error('Viewer未初始化，无法创建标签')
      throw new Error('Viewer not initialized')
    }

    // 创建DOM元素
    const labelElement = document.createElement('div')

    // 应用默认样式和自定义样式
    const mergedStyle = { ...this.createDefaultStyle(), ...(options.style || {}) }
    this.applyStyle(labelElement, mergedStyle)

    // 添加自定义类名
    if (mergedStyle.className) {
      labelElement.className = mergedStyle.className
    }

    // 设置文本和图片内容
    const hasText = options.text !== undefined
    const hasImage = options.image !== undefined

    // 如果同时有文本和图片，文本在上，图片在下
    if (hasText && hasImage && options.image) {
      // 设置标签容器为flex布局，垂直排列
      labelElement.style.display = 'flex'
      labelElement.style.flexDirection = 'column'
      labelElement.style.alignItems = 'center'
      labelElement.style.justifyContent = 'center'

      // 添加文本内容
      const textElement = document.createElement('div')
      if (typeof options.text === 'string') {
        textElement.textContent = options.text
      } else if (options.text instanceof HTMLElement) {
        textElement.appendChild(options.text)
      }
      // 文本样式
      textElement.style.marginBottom = '5px'
      textElement.style.textAlign = 'center'
      textElement.style.width = '100%'
      labelElement.appendChild(textElement)

      // 添加图片内容
      const imgElement = document.createElement('img')
      const imageOptions = options.image
      imgElement.src = imageOptions.src
      if (imageOptions.width) imgElement.style.width = imageOptions.width
      if (imageOptions.height) imgElement.style.height = imageOptions.height
      if (imageOptions.alt) imgElement.alt = imageOptions.alt
      imgElement.style.display = 'block'
      imgElement.style.margin = '0 auto'
      labelElement.appendChild(imgElement)
    } else if (hasText) {
      // 只有文本内容
      if (typeof options.text === 'string') {
        labelElement.textContent = options.text
      } else if (options.text instanceof HTMLElement) {
        labelElement.appendChild(options.text)
      }
      labelElement.style.textAlign = 'center'
    } else if (hasImage && options.image) {
      // 只有图片内容
      const imgElement = document.createElement('img')
      const imageOptions = options.image
      imgElement.src = imageOptions.src
      if (imageOptions.width) imgElement.style.width = imageOptions.width
      if (imageOptions.height) imgElement.style.height = imageOptions.height
      if (imageOptions.alt) imgElement.alt = imageOptions.alt
      imgElement.style.display = 'block'
      imgElement.style.margin = '0 auto'
      labelElement.appendChild(imgElement)
    }

    // 创建CSS2D对象
    const labelObject = new CSS2DObject(labelElement)

    // 设置位置
    const position =
      options.position instanceof THREE.Vector3
        ? options.position
        : new THREE.Vector3(...options.position)
    labelObject.position.copy(position)
    labelObject.position.set(position.x, position.y, position.z)

    // 设置可见性
    labelObject.visible = options.visible !== undefined ? options.visible : true

    // 添加事件监听
    if (options.onPointerDown) {
      labelElement.addEventListener('pointerdown', options.onPointerDown)
    }
    if (options.onPointerUp) {
      labelElement.addEventListener('pointerup', options.onPointerUp)
    }
    if (options.onPointerEnter) {
      labelElement.addEventListener('pointerenter', options.onPointerEnter)
    }
    if (options.onPointerLeave) {
      labelElement.addEventListener('pointerleave', options.onPointerLeave)
    }
    if (options.onClick) {
      labelElement.style.cursor = 'pointer'
      labelElement.addEventListener('click', options.onClick)
    }

    // 生成唯一ID
    const labelId = options.id || `label-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    const label = Object.assign(labelObject, {
      tag: options.tag,
      group: options.group,
    })
    // 保存到标签集合，包含tag和group信息
    this.labels.set(labelId, label)

    // 添加到场景
    viewer.scene.add(label)
    return label
  }

  /**
   * 更新标签位置
   * @param labelObject 标签对象
   * @param position 新位置
   */
  public updateLabelPosition(
    labelObject: Label,
    position: THREE.Vector3 | [number, number, number],
  ): void {
    if (position instanceof THREE.Vector3) {
      labelObject.position.copy(position)
    } else {
      labelObject.position.set(...position)
    }
  }

  /**
   * 更新标签内容
   * @param labelObject 标签对象
   * @param text 新文本内容，不传则保持现有文本
   * @param image 新图片配置，不传则保持现有图片
   */
  public updateLabelContent(
    labelObject: Label,
    text?: string | HTMLElement,
    image?: ImageOptions,
  ): void {
    const element = labelObject.element

    // 查找现有的文本和图片元素
    let textElement: HTMLDivElement | null = null
    let imgElement: HTMLImageElement | null = null
    let textNode: Text | null = null

    // 遍历子元素，找到现有的文本和图片元素
    for (let i = 0; i < element.childNodes.length; i++) {
      const node = element.childNodes[i]
      if (node.nodeType === Node.ELEMENT_NODE) {
        const el = node as HTMLElement
        if (el.tagName === 'DIV') {
          textElement = el as HTMLDivElement
        } else if (el.tagName === 'IMG') {
          imgElement = el as HTMLImageElement
        }
      } else if (node.nodeType === Node.TEXT_NODE) {
        const tn = node as Text
        if (tn.textContent && tn.textContent.trim()) {
          textNode = tn
        }
      }
    }

    // 处理文本内容更新
    if (text !== undefined) {
      if (typeof text === 'string') {
        // 如果新内容是字符串
        if (textElement) {
          // 如果已有文本元素，直接更新内容
          textElement.textContent = text
        } else if (textNode) {
          // 如果有文本节点，直接更新内容
          textNode.textContent = text
        } else {
          // 创建新的文本元素
          const newTextElement = document.createElement('div')
          newTextElement.textContent = text
          newTextElement.style.textAlign = 'center'

          // 如果有图片元素，将文本元素插入到图片元素之前
          if (imgElement && element.contains(imgElement)) {
            element.insertBefore(newTextElement, imgElement)
          } else {
            element.appendChild(newTextElement)
          }
          textElement = newTextElement
        }
      } else if (text instanceof HTMLElement) {
        // 如果新内容是HTML元素
        if (textElement) {
          // 如果已有文本元素，替换内容
          textElement.innerHTML = ''
          textElement.appendChild(text)
        } else if (textNode) {
          // 如果有文本节点，替换为HTML元素
          const newTextElement = document.createElement('div')
          newTextElement.appendChild(text)
          if (element.contains(textNode)) {
            element.replaceChild(newTextElement, textNode)
          } else {
            element.appendChild(newTextElement)
          }
          textElement = newTextElement
        } else {
          // 直接添加HTML元素
          if (imgElement && element.contains(imgElement)) {
            element.insertBefore(text, imgElement)
          } else {
            element.appendChild(text)
          }
          if (text.tagName === 'DIV') {
            textElement = text as HTMLDivElement
          }
        }
      }
    }

    // 处理图片内容更新
    if (image !== undefined) {
      if (imgElement) {
        // 如果已有图片元素，更新属性
        imgElement.src = image.src
        if (image.width) imgElement.style.width = image.width
        if (image.height) imgElement.style.height = image.height
        if (image.alt) imgElement.alt = image.alt
      } else {
        // 创建新的图片元素
        const newImgElement = document.createElement('img')
        newImgElement.src = image.src
        if (image.width) newImgElement.style.width = image.width
        if (image.height) newImgElement.style.height = image.height
        if (image.alt) newImgElement.alt = image.alt
        newImgElement.style.display = 'block'
        newImgElement.style.margin = '0 auto'
        element.appendChild(newImgElement)
        imgElement = newImgElement
      }
    }

    // 更新布局样式
    if ((textElement || textNode) && imgElement) {
      // 如果同时有文本和图片，设置flex布局
      element.style.display = 'flex'
      element.style.flexDirection = 'column'
      element.style.alignItems = 'center'
      element.style.justifyContent = 'center'

      // 确保文本在上，图片在下
      if (textElement) {
        if (textElement.nextSibling !== imgElement) {
          element.appendChild(textElement)
          element.appendChild(imgElement)
        }

        // 设置文本样式
        textElement.style.marginBottom = '5px'
        textElement.style.textAlign = 'center'
        textElement.style.width = '100%'
      }
    } else if (textElement) {
      // 只有文本
      element.style.textAlign = 'center'
    } else if (imgElement) {
      // 只有图片
      imgElement.style.display = 'block'
      imgElement.style.margin = '0 auto'
    }
  }

  /**
   * 更新标签样式
   * @param labelObject 标签对象
   * @param style 新样式
   */
  public updateLabelStyle(labelObject: CSS2DObject, style: LabelStyle): void {
    this.applyStyle(labelObject.element, style)
  }

  /**
   * 设置标签可见性
   * @param labelObject 标签对象
   * @param visible 是否可见
   */
  public setLabelVisible(labelObject: CSS2DObject, visible: boolean): void {
    labelObject.visible = visible
  }

  /**
   * 移除标签
   * @param labelObject 标签对象
   */
  public removeLabel(labelObject: CSS2DObject): void {
    if (!viewer || !viewer.scene) return

    // 从场景中移除
    viewer.scene.remove(labelObject)

    // 从集合中移除
    for (const [id, labelData] of this.labels.entries()) {
      if (labelData === labelObject) {
        this.labels.delete(id)
        break
      }
    }

    // 清理事件监听
    const element = labelObject.element
    element.replaceWith(element.cloneNode(true))
  }

  /**
   * 通过标签获取标签对象
   * @param tag 标签
   * @returns 符合条件的标签对象数组
   */
  public getLabelsByTag(tag: string): Label[] {
    const result: Label[] = []

    this.labels.forEach((labelData) => {
      if (labelData.tag === tag) {
        result.push(labelData)
      }
    })

    return result
  }

  /**
   * 通过分组获取标签对象
   * @param group 分组
   * @returns 符合条件的标签对象数组
   */
  public getLabelsByGroup(group: string): Label[] {
    const result: Label[] = []

    this.labels.forEach((labelData) => {
      if (labelData.group === group) {
        result.push(labelData)
      }
    })

    return result
  }

  /**
   * 通过标签删除标签对象
   * @param tag 标签
   */
  public removeLabelsByTag(tag: string): void {
    if (!viewer || !viewer.scene) return

    const labelsToRemove: string[] = []

    // 找出所有匹配的标签
    this.labels.forEach((labelData, id) => {
      if (labelData.tag === tag) {
        // 从场景中移除
        viewer.scene.remove(labelData)

        // 清理事件监听
        const element = labelData.element
        element.replaceWith(element.cloneNode(true))

        // 添加到待删除列表
        labelsToRemove.push(id)
      }
    })

    // 从集合中删除
    labelsToRemove.forEach((id) => {
      this.labels.delete(id)
    })
  }

  /**
   * 通过分组删除标签对象
   * @param group 分组
   */
  public removeLabelsByGroup(group: string): void {
    if (!viewer || !viewer.scene) return

    const labelsToRemove: string[] = []

    // 找出所有匹配的标签
    this.labels.forEach((labelData, id) => {
      if (labelData.group === group) {
        // 从场景中移除
        viewer.scene.remove(labelData)

        // 清理事件监听
        const element = labelData.element
        element.replaceWith(element.cloneNode(true))

        // 添加到待删除列表
        labelsToRemove.push(id)
      }
    })

    // 从集合中删除
    labelsToRemove.forEach((id) => {
      this.labels.delete(id)
    })
  }

  /**
   * 移除所有标签
   */
  public removeAllLabels(): void {
    if (!viewer || !viewer.scene) return

    // 从场景中移除所有标签
    this.labels.forEach((labelData) => {
      viewer.scene.remove(labelData)

      // 清理事件监听
      const element = labelData.element
      element.replaceWith(element.cloneNode(true))
    })

    // 清空集合
    this.labels.clear()
  }

  /**
   * 获取所有标签
   * @returns 标签集合
   */
  public getAllLabels(): Map<string, Label> {
    // 转换为旧格式，保持兼容性
    const result = new Map<string, Label>()
    this.labels.forEach((labelData, id) => {
      result.set(id, labelData)
    })
    return result
  }
}

// 导出单例实例
export const labelManager = LabelManager.getInstance()

// 导出类型
export type { LabelOptions, LabelStyle, ImageOptions }
