/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-01-02
 * @LastEditors: 余承
 * @LastEditTime: 2025-06-10
 */
import { useMouse } from '@vueuse/core'
import { ref, watch } from 'vue'

const getTipElement = (tipText: string): HTMLDivElement => {
  document.getElementById('mouse-tip')?.remove()
  const el = document.createElement('div')
  el.textContent = tipText
  el.id = 'mouse-tip'
  el.style.position = 'fixed'
  el.style.zIndex = '999999'
  el.style.padding = '5px 8px'
  el.style.borderRadius = '4px'
  el.style.backgroundColor = 'rgba(9, 61, 126,0.8)'
  return el
}

const tipText = ref('')
const showTip = ref(false)

export const useMouseTip = (options?: { text: string; style?: Partial<CSSStyleDeclaration> }) => {
  tipText.value = options?.text ?? ''
  const el = getTipElement(tipText.value)
  if (options?.style) {
    for (let key in options.style) {
      el.style[key] = options.style[key] as string
    }
  }
  document.body.appendChild(el)
  watch(tipText, (val) => {
    el.textContent = val
  })
  watch(
    showTip,
    (val) => {
      if (val) {
        el.style.display = 'block'
      } else {
        el.style.display = 'none'
      }
    },
    {
      immediate: true,
    },
  )
  useMouse({
    type: (e: MouseEvent | Touch) => {
      const { pageX, pageY } = e
      el.style.left = `${pageX + 15}px`
      el.style.top = `${pageY}px`
      return [pageX, pageY]
    },
  })
  return {
    tipText,
    showTip,
  }
}
