/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-12-04
 * @LastEditors: 余承
 * @LastEditTime: 2025-06-22
 */
import { type ShallowRef, shallowRef, onMounted, onBeforeUnmount } from 'vue'
import echarts from '@/utils/echarts'
import type { EChartsOption } from 'echarts'
import { baseChartOptions } from '@/config/echart'
import { merge } from 'lodash-es'

const useEcharts = (elRef: Readonly<ShallowRef<HTMLDivElement | null>>, options: EChartsOption) => {
  const chart = shallowRef<echarts.ECharts>()
  // echart实例
  let chartInstance = null as any
  const initCharts = () => {
    chart.value = echarts.init(elRef.value)
    chartInstance = chart.value
    setOptions(merge({}, baseChartOptions, options))
  }
  const setOptions = (options: EChartsOption) => {
    chart.value && chart.value.setOption(options)
  }
  const echartsResize = () => {
    if (chart.value) {
      const { width, height } = elRef.value!.getBoundingClientRect()
      if (width && height) {
        chart.value.resize()
      }
    }
  }

  // 获取echart实例
  function getInstance() {
    if (!chartInstance) {
      initCharts()
    }
    return chartInstance
  }

  // onMounted(() => {
  //   window.addEventListener('resize', echartsResize)
  // })

  // onBeforeUnmount(() => {
  //   window.removeEventListener('resize', echartsResize)
  // })

  return {
    chart,
    initCharts,
    setOptions,
    echartsResize,
    getInstance,
  }
}
export { useEcharts }
