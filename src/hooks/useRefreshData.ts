/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-03-16
 * @LastEditors: 余承
 * @LastEditTime: 2025-03-16
 */
import { onBeforeUnmount, onMounted } from 'vue'

export const useDataRefresh = (fn: () => void, delay: number = 1000 * 10) => {
  let timer: NodeJS.Timeout | null = null

  const start = () => {
    timer = setInterval(fn, delay)
  }

  const stop = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }
  onMounted(() => {
    fn()
    start()
  })
  onBeforeUnmount(() => {
    stop()
  })
}
