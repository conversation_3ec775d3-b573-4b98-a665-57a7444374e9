import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

// 扩展 dayjs 插件
dayjs.extend(duration)

/**
 * @description: 获取assets图片
 * @param {string} name 图片名称
 * @returns {string} 图片路径
 */
export function getAssetsImage(name: string, folder = 'overview') {
  return new URL(`/src/assets/images/${folder}/${name}`, import.meta.url).href
}

type CountdownOptions = {
  /** 倒计时秒数 */
  seconds?: number
  /** 目标时间（dayjs 支持的任意时间格式） */
  targetTime?: dayjs.ConfigType
  /** 显示格式 */
  format?: string
  /** 每秒回调 */
  onTick?: (data: {
    secondsLeft: number
    formatted: string
    timeLeft: {
      days: number
      hours: number
      minutes: number
      seconds: number
    }
    targetTime?: dayjs.Dayjs
    currentTime: dayjs.Dayjs
  }) => void
  /** 结束回调 */
  onFinish?: () => void
  /** 开始回调 */
  onStart?: () => void
}

type CountdownController = {
  stop: () => void
  reset: () => void
  isRunning: () => boolean
  getRemainingTime: () => number
  getTargetTime: () => dayjs.Dayjs | null
  getCurrentTime: () => dayjs.Dayjs
}

/**
 * @description: 格式化时间
 * @param time 时间
 * @param format 格式
 * @returns 格式化后的时间
 */
export function formatTime(time: dayjs.ConfigType, format = 'HH:mm:ss'): string {
  return dayjs(time).format(format)
}

/**
 * @description: 格式化倒计时时间
 * @param seconds 总秒数
 * @param format 格式化字符串
 * @returns 格式化后的时间字符串
 */
export function formatCountdownTime(seconds: number, format = 'HH:mm:ss'): string {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  // 处理中文格式
  if (
    format.includes('天') ||
    format.includes('时') ||
    format.includes('分') ||
    format.includes('秒')
  ) {
    return format
      .replace(/D+天?/g, `${days}天`)
      .replace(/H+时?/g, `${hours}时`)
      .replace(/m+分?/g, `${minutes}分`)
      .replace(/s+秒?/g, `${secs}秒`)
  }

  // 处理常见英文格式
  switch (format) {
    case 'HH:mm:ss':
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    case 'mm:ss':
      return `${(hours * 60 + minutes).toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    case 'ss':
      return secs.toString().padStart(2, '0')
    case 'D天H时m分s秒':
      return `${days}天${hours}时${minutes}分${secs}秒`
    case 'D天HH:mm:ss':
      return `${days}天${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    default:
      try {
        // 尝试使用 dayjs 格式化
        const baseTime = dayjs().startOf('day').add(seconds, 'second')
        return baseTime.format(format)
      } catch {
        // 如果格式化失败，返回默认格式
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
  }
}

/**
 * @description: 计算时间差
 * @param targetTime 目标时间
 * @param currentTime 当前时间
 * @returns 时间差对象
 */
export function calculateTimeDiff(targetTime: dayjs.Dayjs, currentTime: dayjs.Dayjs = dayjs()) {
  const diff = targetTime.diff(currentTime, 'second')
  const seconds = Math.max(0, diff)

  return {
    totalSeconds: seconds,
    days: Math.floor(seconds / 86400),
    hours: Math.floor((seconds % 86400) / 3600),
    minutes: Math.floor((seconds % 3600) / 60),
    seconds: seconds % 60,
  }
}

/**
 * @description: 倒计时
 * @param options 倒计时选项
 * @returns 倒计时控制器
 */
export function countdown(options: CountdownOptions): CountdownController {
  const { seconds, targetTime, format = 'HH:mm:ss', onTick, onFinish, onStart } = options

  // 参数验证
  if (!seconds && !targetTime) {
    throw new Error('必须提供 seconds 或 targetTime 参数')
  }

  let timer: number | null = null
  let isRunning = false
  let target: dayjs.Dayjs | null = null
  let initialSeconds: number = 0
  let remainingSeconds: number = 0
  let useTargetTime = false

  // 初始化目标时间和初始秒数
  if (targetTime) {
    target = dayjs(targetTime)
    const now = dayjs()
    if (target.isBefore(now)) {
      throw new Error('目标时间不能早于当前时间')
    }
    initialSeconds = target.diff(now, 'second')
    remainingSeconds = initialSeconds
    useTargetTime = true
  } else if (seconds) {
    if (seconds <= 0) {
      throw new Error('倒计时秒数必须大于0')
    }
    initialSeconds = seconds
    remainingSeconds = seconds
    target = dayjs().add(seconds, 'second')
    useTargetTime = false
  }

  const tick = () => {
    if (useTargetTime && target) {
      // 使用目标时间模式，实时计算剩余时间
      const now = dayjs()
      const timeDiff = calculateTimeDiff(target, now)
      remainingSeconds = timeDiff.totalSeconds

      if (remainingSeconds <= 0) {
        stop()
        onFinish?.()
        return
      }

      onTick?.({
        secondsLeft: remainingSeconds,
        formatted: formatCountdownTime(remainingSeconds, format),
        timeLeft: {
          days: timeDiff.days,
          hours: timeDiff.hours,
          minutes: timeDiff.minutes,
          seconds: timeDiff.seconds,
        },
        targetTime: target,
        currentTime: now,
      })
    } else {
      // 使用秒数模式，使用内部计数器
      if (remainingSeconds <= 0) {
        stop()
        onFinish?.()
        return
      }

      const days = Math.floor(remainingSeconds / 86400)
      const hours = Math.floor((remainingSeconds % 86400) / 3600)
      const minutes = Math.floor((remainingSeconds % 3600) / 60)
      const secs = remainingSeconds % 60

      onTick?.({
        secondsLeft: remainingSeconds,
        formatted: formatCountdownTime(remainingSeconds, format),
        timeLeft: {
          days,
          hours,
          minutes,
          seconds: secs,
        },
        targetTime: target || undefined,
        currentTime: dayjs(),
      })

      remainingSeconds--
    }
  }

  const start = () => {
    if (isRunning) return

    isRunning = true
    onStart?.()

    tick() // 立即执行一次
    timer = window.setInterval(tick, 1000)
  }

  const stop = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    isRunning = false
  }

  const reset = () => {
    stop()
    // 重新计算目标时间和剩余秒数
    if (targetTime) {
      target = dayjs(targetTime)
      const now = dayjs()
      remainingSeconds = target.diff(now, 'second')
    } else if (seconds) {
      remainingSeconds = initialSeconds
      target = dayjs().add(initialSeconds, 'second')
    }
  }

  // 自动开始
  start()

  return {
    stop,
    reset,
    isRunning: () => isRunning,
    getRemainingTime: () => remainingSeconds,
    getTargetTime: () => target,
    getCurrentTime: () => dayjs(),
  }
}
