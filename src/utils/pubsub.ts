/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-11-12
 * @LastEditors: 余承
 * @LastEditTime: 2025-02-06
 */
// 消息回调
type messageCallback<M = any> = (message: M) => void

/**
 * @description: 发布订阅
 */
class Pubsub<T = Record<string, any>> {
  /**
   * @description: 订阅的消息集合
   */
  private subscribeList = new Map<string, messageCallback[]>()
  /**
   * @description: 发布消息
   * @param {string} messagekey 消息名称
   * @param {any} messageInfo 消息内容
   */
  publish<k extends keyof T>(messagekey: k, messageInfo: any) {
    const list = this.subscribeList.get(messagekey as string)
    if (list !== undefined) {
      for (const callback of list) {
        callback(messageInfo)
        //@ts-ignore 自动销毁
        if (callback['once'] === true) {
          this.unsubscribe(messagekey, callback)
        }
      }
    }
  }
  /**
   * @description: 订阅消息
   * @param {string} messagekey 消息名
   * @param {function} callback 接收到消息时的回调
   * @param {boolean} once 是否在执行一次后自动销毁， 默认不销毁
   */
  subscribe<k extends keyof T>(messagekey: k, callback: messageCallback<T[k]>, once = false) {
    const list = this.subscribeList.get(messagekey as string)
    if (once === true) {
      //@ts-ignore
      callback['once'] = true
    }
    if (list !== undefined) {
      this.subscribeList.set(messagekey as string, Array.from(new Set([...list, callback])))
    } else {
      this.subscribeList.set(messagekey as string, [callback])
    }
  }
  /**
   * @description: 取消订阅
   * @param {string} messagekey 消息名
   * @param {string} callbackKey 接收到消息时的回调
   */
  unsubscribe<k extends keyof T>(messagekey: k, callback: messageCallback) {
    const list = this.subscribeList.get(messagekey as string)
    if (list !== undefined) {
      this.subscribeList.set(
        messagekey as string,
        list.filter((item) => item !== callback),
      )
    }
  }
  /**
   * @description: 清空订阅
   */
  public clear() {
    this.subscribeList.clear()
  }
}

export default Pubsub
