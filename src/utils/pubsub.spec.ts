/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-11-14
 * @LastEditors: 余承
 * @LastEditTime: 2024-11-14
 */
import { describe, beforeEach, test, expect, vi } from 'vitest'
import Pubsub from './pubsub'

describe('Pubsub', () => {
  let pubsub: Pubsub

  beforeEach(() => {
    pubsub = new Pubsub()
  })

  test('should publish a message to subscribers', () => {
    const mockCallback = vi.fn()
    pubsub.subscribe('testMessage', mockCallback)
    pubsub.publish('testMessage', { data: 'test data' })
    expect(mockCallback).toHaveBeenCalledWith({ data: 'test data' })
  })

  test('should not call unsubscribed callbacks', () => {
    const mockCallback = vi.fn()
    const mockCallback2 = vi.fn()
    pubsub.subscribe('testMessage', mockCallback)
    pubsub.unsubscribe('testMessage', mockCallback)

    pubsub.publish('testMessage', { data: 'test data' })

    expect(mockCallback).not.toHaveBeenCalled()
    expect(mockCallback2).not.toHaveBeenCalled()
  })

  test('should only call the callback once if specified', () => {
    const mockCallback = vi.fn()
    pubsub.subscribe('testMessage', mockCallback, true)

    pubsub.publish('testMessage', { data: 'test data' })
    pubsub.publish('testMessage', { data: 'test data again' })

    expect(mockCallback).toHaveBeenCalledTimes(1)
  })

  test('should allow multiple subscriptions to the same message', () => {
    const mockCallback1 = vi.fn()
    const mockCallback2 = vi.fn()
    pubsub.subscribe('testMessage', mockCallback1)
    pubsub.subscribe('testMessage', mockCallback2)
    pubsub.publish('testMessage', { data: 'test data' })
    expect(mockCallback1).toHaveBeenCalledWith({ data: 'test data' })
    expect(mockCallback2).toHaveBeenCalledWith({ data: 'test data' })
  })

  test('should not crash when publishing to an unsubscribed message', () => {
    expect(() => {
      pubsub.publish('nonExistentMessage', { data: 'test data' })
    }).not.toThrow()
  })
})
