/*
 * @Description: axios 请求封装
 * @Author: yucheng
 * @Date: 2024-01-17
 */
import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// 接口返回数据类型
interface IResponse<T = any> {
  code: number
  message: string
  data: T
  info?: string
}

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: 'http://************:8888', // API 请求的默认前缀
  timeout: 1000 * 30, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  },
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求发送之前做一些处理，例如添加 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['x-token'] = token
    }
    if (config.url?.startsWith('/cim')) {
      config.headers['freedo-cim-authorization'] = localStorage.getItem('cimToken')
    }
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<IResponse>) => {
    const res = response.data

    // 这里可以根据后端的约定判断请求是否成功
    if (res.code === 0 || res.code === 200) {
      return res.data
    }

    // 处理特定错误码
    if (res.code === 401) {
      // token 过期或未登录
      sessionStorage.removeItem('token')
      // 可以在这里添加重定向到登录页的逻辑
    }

    console.error('接口请求失败:', res.message || res.info)
    return Promise.reject(res.message || res.info || '请求失败')
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    return Promise.reject(error)
  },
)

/**
 * @description: GET 请求
 * @param {string} url 请求地址
 * @param {object} params 请求参数
 * @param {object} config 额外配置
 */
export function get<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.get(url, { params, ...config })
}

/**
 * @description: POST 请求
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @param {object} config 额外配置
 */
export function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.post(url, data ?? {}, config)
}

/**
 * @description: PUT 请求
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @param {object} config 额外配置
 */
export function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.put(url, data, config)
}

/**
 * @description: DELETE 请求
 * @param {string} url 请求地址
 * @param {object} config 额外配置
 */
export function del<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
  return service.delete(url, config)
}

// 导出 axios 实例
export default service
