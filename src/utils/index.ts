/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-01-06
 * @LastEditors: 余承
 * @LastEditTime: 2025-03-25
 */

/**
 * @description: 睡眠函数，用于阻塞进程
 * @param {number} time 睡眠时间 单位毫秒
 */
export const sleep = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

// 压缩base64图片
export const compressBase64Image = (
  base64: string,
  maxWidth: number,
  maxHeight: number,
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.src = base64
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      const MAX_WIDTH = maxWidth
      const MAX_HEIGHT = maxHeight
      let width = img.width
      let height = img.height
      if (width > height) {
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width
          width = MAX_WIDTH
        }
      } else {
        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height
          height = MAX_HEIGHT
        }
      }
      canvas.width = width
      canvas.height = height
      ctx.drawImage(img, 0, 0, width, height)
      const dataURL = canvas.toDataURL('image/jpeg', 0.8)
      resolve(dataURL)
    }
  })
}

/**
 * @description: 将数组转换为对象
 * @param {any[]} array 数组
 * @param {string} key 键
 * @return {*}
 */
export const arrayToObject = (array: any[], key: string) => {
  return array.reduce((acc, item) => {
    acc[item[key]] = item
    return acc
  }, {})
}

/**
 * @description: 复制文本
 * @param {string} text
 * @return {*}
 */
export const copyText = async (text: string) => {
  try {
    // 优先使用 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
    } else {
      // 降级使用 execCommand
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    console.log('复制成功')
  } catch (error) {
    console.error('复制失败', error)
  }
}

/**
 * @description: 等待条件成立
 * @param {() => boolean} condition 条件
 * @param {number} interval 间隔时间 单位毫秒 默认100
 * @param {number} timeout 超时时间 单位毫秒 默认10000
 * @return {*}
 */
export const until = (
  condition: () => boolean | Promise<boolean>,
  interval = 100,
  timeout = 1000 * 30,
) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    const checkCondition = async () => {
      if (await condition()) {
        resolve(true)
      } else if (Date.now() - startTime > timeout) {
        resolve('超时')
      } else {
        setTimeout(checkCondition, interval)
      }
    }
    setTimeout(checkCondition, interval)
  })
}
