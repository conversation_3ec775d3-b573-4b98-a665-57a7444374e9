<!--
 * @Description: 楼宇详情弹窗组件
 * @Author: yucheng
 * @Date: 2025-06-22
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-08
-->
<template>
  <Transition name="modal">
    <div class="modal-overlay" v-if="isOpen">
      <div class="modal-container">
        <div class="modal-header">
          <span class="modal-title">{{ title }}</span>
          <Icon class="close-icon" name="guanbi1" title="关闭" @click="handleClose" />
        </div>
        <div class="modal-body" ref="mapContainer"></div>
        <button class="clear-btn">清除</button>
        <div class="modal-footer">
          <button class="btn-confirm" @click="handleConfirm">确认</button>
          <button class="btn-close" @click="handleClose">取消</button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script lang="ts" setup>
import Icon from '@/components/Icon/Icon.vue'
import { threeJSEvents } from '@/common/evens'
import { viewer } from '@/common/viewer'
import { nextTick, ref, useTemplateRef, watch } from 'vue'
import { GeometryDrawer } from '@/common/geometryDrawer'
import highlightManager from '@/common/highLight'
import { navigate } from '@/common/navigate'
import { clearRoomName, createRoomName, update26AVisible } from '@/views/area-overview'
import * as THREE from 'three'
import { resourceManager } from '@/common/resource'
import { labelManager } from '@/common/label'
import { sceneConfig } from '@/config/scene'

const title = ref('')

let showScene: boolean | undefined = undefined
const isOpen = ref(false)
const mapContainer = useTemplateRef<HTMLDivElement>('mapContainer')
let geometryDrawer: GeometryDrawer | null = null

const loadFloor = (floor: number) => {
  const floorModels = ['/models/单层展示.glb']
  Promise.all(floorModels.map((model) => resourceManager.loadGLTF(model))).then((gltf) => {
    viewer.scene.add(...gltf)
    highlightManager.clearAllHighlights()
    gltf[0].visible = true
    createRoomName()
    navigate.flyTo(
      new THREE.Vector3(-79.52791255788605, 196.7879994203817, 411.8978496471575),
      new THREE.Vector3(-154.96231628221057, 83.92327838133666, 416.7103694155604),
    )
    update26AVisible(false)
  })
}
const open = async () => {
  isOpen.value = true
  await nextTick()
  const _viewer = document.querySelector('#viewer')! as HTMLDivElement
  _viewer.style.position = 'absolute'
  showScene = _viewer.style.display !== 'none'
  if (!showScene) {
    _viewer.style.display = 'block'
  }
  mapContainer.value?.appendChild(_viewer!)
  viewer.renderer.setSize(mapContainer.value!.clientWidth, mapContainer.value!.clientHeight)
  viewer.renderer.render(viewer.scene, viewer.camera)
  threeJSEvents.publish('scene:resize', {
    width: mapContainer.value!.clientWidth,
    height: mapContainer.value!.clientHeight,
  })
  loadFloor(5)
  const floorModels = ['/models/单层矢量面.glb']
  Promise.all(floorModels.map((model) => resourceManager.loadGLTF(model))).then((gltf) => {
    gltf[0].name = '单层矢量面'
    // @ts-ignore
    const changedRoom = window.changedRoom === true
    gltf[0].getObjectByName('Plane003')!.visible = changedRoom
    viewer.scene.add(...gltf)
    highlightManager.clearAllHighlights()
  })
  labelManager.getLabelsByTag('building-name').forEach((label) => {
    label.visible = false
  })
  geometryDrawer = new GeometryDrawer(viewer.scene, viewer.camera, viewer.renderer)
  geometryDrawer.startDraw(
    (points) => {
      console.log(points)
      viewer.scene.getObjectByName('Plane003')!.visible = true
      labelManager.getAllLabels().get('05-04')!.visible = true
    },
    () => {
      // @ts-ignore
      window.changedRoom = false
      viewer.scene.getObjectByName('Plane003')!.visible = false
      labelManager.getAllLabels().get('05-04')!.visible = false
      options?.onCancel?.()
      options = null
      handleClose()
    },
  )
}

const close = () => {
  const _viewer = document.querySelector('#viewer')! as HTMLDivElement
  _viewer.style.position = 'fixed'
  if (!showScene) {
    _viewer.style.display = 'none'
  }
  const container = document.querySelector('.main-content')
  container?.insertBefore(_viewer!, container.firstChild)
  viewer.renderer.setSize(1920, 1080)
  viewer.renderer.render(viewer.scene, viewer.camera)
  threeJSEvents.publish('scene:resize', {
    width: 1920,
    height: 1080,
  })
  update26AVisible(true)
  viewer.scene.children.forEach((child) => {
    if (child instanceof THREE.Group && child.name === '单层矢量面') {
      viewer.scene.remove(child)
    }
  })
  labelManager.getLabelsByTag('building-name').forEach((label) => {
    label.visible = true
  })
  clearRoomName()
  navigate.flyTo(
    new THREE.Vector3(...sceneConfig.center.position),
    new THREE.Vector3(...sceneConfig.center.target),
  )
  highlightManager.clearAllHighlights()
  isOpen.value = false
  geometryDrawer?.cancelDraw()
}

watch(isOpen, (newVal) => {
  if (newVal) {
    open()
  } else {
    close()
  }
})

let options: {
  title: string
  onConfirm?: Function
  onCancel?: Function
} | null = null

// @ts-ignore
window.showMapModal = (_options: { title: string; onConfirm?: Function; onCancel?: Function }) => {
  title.value = _options.title ?? '划分区域'
  options = _options
  isOpen.value = true
}

// @ts-ignore
window.hideMapModal = () => {
  isOpen.value = false
}
const handleClose = () => {
  // @ts-ignore
  window.changedRoom = false
  isOpen.value = false
  geometryDrawer?.cancelDraw()
  options?.onCancel?.()
  options = null
}
const handleConfirm = () => {
  geometryDrawer?.cancelDraw()
  options?.onConfirm?.('#05-04')
  // @ts-ignore
  window.changedRoom = true
  isOpen.value = false
  options = null
}
</script>

<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  position: relative;
  width: 1200px;
  height: 800px;
  max-height: 90vh;
  background-color: #1a2233;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(51, 125, 255, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #337dff, #2c6ad9);
  color: white;
  border-radius: 8px 8px 0 0;
  position: relative;
  box-shadow: 0 2px 6px rgba(51, 125, 255, 0.2);

  .modal-title {
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    width: 100%;
    letter-spacing: 0.5px;
  }

  .close-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.2s;

    &:hover {
      color: #fff;
      transform: translateY(-50%) scale(1.1);
    }
  }
}

.modal-body {
  position: relative;
  flex: 1;
  height: 500px;
  overflow: hidden;
  color: #e6f0ff;
}

.clear-btn {
  position: absolute;
  bottom: 80px;
  right: 10px;
  z-index: 1000;
  background-color: #337dff;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  &:hover {
    background-color: #5095ff;
  }
}

.section {
  margin-bottom: 24px;

  .section-title {
    font-size: 15px;
    font-weight: 500;
    color: #e6f0ff;
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(51, 125, 255, 0.3);
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #337dff, #5095ff);
      border-radius: 2px;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 4px;
}

.info-item {
  display: flex;
  background-color: #23304e;
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s;
  border: 1px solid rgba(51, 125, 255, 0.2);

  &:hover {
    box-shadow: 0 3px 8px rgba(51, 125, 255, 0.2);
    border-color: rgba(51, 125, 255, 0.5);
    transform: translateY(-2px);
    background-color: #2a385a;
  }

  .info-label {
    min-width: 100px;
    color: #a0a0a0;
    font-size: 14px;
    font-weight: 500;
  }

  .info-value {
    flex: 1;
    color: #e6f0ff;
    font-size: 14px;

    &.description {
      line-height: 1.6;
    }
  }
}

.modal-footer {
  width: 100%;
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid rgba(51, 125, 255, 0.2);
  background-color: #1e2738;

  .btn-confirm {
    padding: 8px 24px;
    border-radius: 4px;
    border: 1px solid #337dff;
    background-color: rgba(51, 125, 255, 0.15);
    color: #337dff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

    &:hover {
      background-color: #337dff;
      color: #fff;
      box-shadow: 0 4px 10px rgba(51, 125, 255, 0.4);
    }
  }

  .btn-close {
    padding: 8px 24px;
    margin-left: 10px;
    border-radius: 4px;
    border: 1px solid #337dff;
    background-color: #1e2738;
    color: #337dff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

    &:hover {
      background-color: #337dff;
      color: #fff;
      box-shadow: 0 4px 10px rgba(51, 125, 255, 0.4);
    }
  }
}

// 模态框过渡动画
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;

  .modal-container {
    transition:
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.3s ease;
  }
}

.modal-enter-from {
  opacity: 0;

  .modal-container {
    transform: translateY(20px);
    opacity: 0;
  }
}

.modal-leave-to {
  opacity: 0;

  .modal-container {
    transform: translateY(20px);
    opacity: 0;
  }
}
</style>
