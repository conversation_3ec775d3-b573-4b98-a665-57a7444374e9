<template>
  <Teleport to=".screen-wrapper">
    <Transition name="modal">
      <div v-if="visible" class="modal-overlay" :style="style" @click="handleOverlayClick">
        <div class="modal-container" @click.stop>
          <div class="line"></div>
          <div class="line"></div>
          <div class="line"></div>
          <div class="line"></div>

          <!-- Header -->
          <div class="modal-header">
            <img src="@/assets/images/dataModal/trangle.png" alt="" class="trangle" />
            <span class="modal-title">
              {{ title }}
            </span>
            <span class="modal-close" v-if="showClose" @click="handleCloseClick">×</span>
          </div>
          <div class="h-[2px]" style="background: linear-gradient(to right, #9dcae6 0%, rgba(255, 255, 255, 0) 100%)">
          </div>

          <!-- Body -->
          <div class="modal-body">
            <slot> </slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import type { CSSProperties } from 'vue'

const props = withDefaults(defineProps<{
  title: string
  style?: CSSProperties
  showClose?: boolean
}>(), {
  showClose: true
})

const emit = defineEmits(['close', 'confirm', 'cancel'])

const visible = defineModel<boolean>('visible', { default: false })

const handleOverlayClick = () => {
  visible.value = false
}

const handleCloseClick = () => {
  visible.value = false
  emit('close')
}

const handleCancelClick = () => {
  emit('cancel')
}

const handleConfirmClick = () => {
  emit('confirm')
}
</script>

<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  position: relative;
  padding: 0 24px 24px;
  max-width: 80vw;
  max-height: 80vh;
  // min-width: 380px;
  // min-height: 204px;
  background-color: rgba(0, 62, 116, 0.85);
  border: 2px solid #0d65b1;

  .line {
    position: absolute;
    width: 18px;
    height: 2px;
    background-color: #75d1fe;

    &:nth-child(1) {
      top: -3px;
      left: -2px;
    }

    &:nth-child(2) {
      top: -3px;
      right: -2px;
    }

    &:nth-child(3) {
      bottom: -3px;
      left: -2px;
    }

    &:nth-child(4) {
      bottom: -3px;
      right: -2px;
    }
  }
}

.modal-content {
  margin-top: 30px;
  font-size: 24px;
  color: #ffffff;
  text-align: center;
}

.modal-header {
  @apply flex items-center justify-between w-full h-16 text-5 pl-1;

  .trangle {
    @apply absolute left-2 w-14px h-14px;
  }
}

.modal-title {
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modal-close {
  font-family: 'SourceHanSansCN';
  font-size: 22px;
  color: #fff;
  margin-left: 20px;
  cursor: pointer;
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;

  .modal-container {
    transition:
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.3s ease;
  }
}

.modal-enter-from {
  opacity: 0;

  .modal-container {
    transform: translateY(20px);
    opacity: 0;
  }
}

.modal-leave-to {
  opacity: 0;

  .modal-container {
    transform: translateY(20px);
    opacity: 0;
  }
}
</style>
