<template>
  <Container title="环境信息">
    <div class="h-45 pl-2.5 pt-7.5">
      <div
        class="flex items-center gap-x-6 font-['ShiShangZhongHeiJianTi'] leading-[27px] text-[22px] text-[#CDD6E3]"
      >
        <span>{{ date }}</span>
        <span>{{ time }}</span>
        <span>{{ week }}</span>
      </div>
      <div class="flex items-center justify-between mt-6">
        <div class="flex flex-col items-center" v-for="(item, index) in envListData" :key="index">
          <img :src="item.icon" :alt="item.label" class="size-[50px]" />
          <span class="text-lg text-white font-['AlibabaPuHuiTi']">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { onBeforeMount, onBeforeUnmount, ref, computed } from 'vue'
import { getAssetsImage } from '@/utils/common'
import { getEnvironmentInfoApi } from '@/api/station'
import Container from '@/components/Container/Container.vue'

let timer: ReturnType<typeof setTimeout> | null = null

// 年月日
const date = ref()
const time = ref()
const week = ref()
const weekMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

const renderCountdown = () => {
  date.value = dayjs().format('YYYY/MM/DD')
  time.value = dayjs().format('HH:mm:ss')
  week.value = weekMap[Number(dayjs().format('d'))]

  timer = setTimeout(() => {
    renderCountdown()
  }, 1000)
}

// 环境信息
const envInfoData = ref<any>({})

const envListData = computed(() => {
  return [
    {
      label: envInfoData.value.weather ?? '--',
      icon: getAssetsImage('无雨雪.png'),
    },
    {
      label: `${envInfoData.value.temperature ?? 0}℃`,
      icon: getAssetsImage('气温.png'),
    },
    {
      label: `${envInfoData.value.humidity ?? 0}%`,
      icon: getAssetsImage('湿度.png'),
    },
    {
      label: `${envInfoData.value.atm ?? 0}hPa`,
      icon: getAssetsImage('气压.png'),
    },
    {
      label: `${envInfoData.value.speed ?? 0}m/s`,
      icon: getAssetsImage('风速.png'),
    },
    {
      label: envInfoData.value.direction ?? '--',
      icon: getAssetsImage('风向.png'),
    },
  ]
})

const fetchEnvironmentInfoData = async () => {
  const res = await getEnvironmentInfoApi()
  envInfoData.value = res
}

onBeforeMount(() => {
  renderCountdown()
  fetchEnvironmentInfoData()
})
onBeforeUnmount(() => {
  timer && clearTimeout(timer)
})
</script>

<style scoped></style>
