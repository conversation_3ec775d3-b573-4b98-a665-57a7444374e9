<template>
  <Container title="提醒信息">
    <div>
      <div class="px-4 space-y-2 pt-4">
        <div>
          <!-- 倒计时 -->
          <div class="flex justify-center items-center mb-2 gap-x-5">
            <span class="text-lg">倒计时:</span>
            <div
              class="min-w-60 h-11 px-6 bg-black/39 border-1 border-solid border-[#32DCFB] flex items-center justify-center gap-x-2.5"
            >
              <span
                class="font-['DS-Digital-Bold'] text-[#FFD631] text-[42px]"
                v-for="(t, i) in countdownTimeComputed"
                :key="i"
              >
                {{ t }}
              </span>
            </div>
          </div>
          <!-- 开始时间/结束时间 -->
          <div class="flex items-center gap-x-2.5">
            <img src="@/assets/images/overview/重保期.png" alt="" class="size-[50px] shrink-0" />
            <div class="text-lg text-white flex-1">
              <p>开始时间：2025-05-27 14:12:20</p>
              <p>结束时间：2025-05-27 14:12:20</p>
            </div>
          </div>
        </div>

        <div>
          <!-- 倒计时 -->
          <div class="flex justify-center items-center mb-2 gap-x-5">
            <span class="text-lg">倒计时:</span>
            <div
              class="min-w-60 h-11 px-6 bg-black/39 border-1 border-solid border-[#32DCFB] flex items-center justify-center gap-x-2.5"
            >
              <span
                class="font-['DS-Digital-Bold'] text-[#FFD631] text-[42px]"
                v-for="(t, i) in countdownTimeComputed"
                :key="i"
              >
                {{ t }}
              </span>
            </div>
          </div>
          <!-- 开始时间/结束时间 -->
          <div class="flex items-center gap-x-2.5">
            <img src="@/assets/images/overview/日凌.png" alt="" class="size-[50px] shrink-0" />
            <div class="text-lg text-white flex-1">
              <p>开始时间：2025-05-27 14:12:20</p>
              <p>结束时间：2025-05-27 14:12:20</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 滚动列表 -->
      <div class="px-3 pt-3">
        <div
          class="flex items-center justify-between px-2 h-10 text-lg font-['AlibabaPuHuiTi'] border-b border-b-solid border-white/74"
        >
          <div v-for="item in remindTypesData" :key="item.id" class="flex items-center gap-x-2.5">
            <span>{{ remindTypesMap[String(item.type)] }}</span>
            <span class="font-['PangMenZhengDao'] text-[#00FFFF] text-2xl">{{ item.count }}</span>
          </div>
        </div>

        <div class="h-30">
          <LoopScroll :dataSource="tipsListData" :loadCount="3" :speed="0.3">
            <template #default="{ item }">
              <div class="scroll-item">
                <span class="shrink-0">{{ item.type }}:</span>
                <span class="shrink-0">{{ item.time }}</span>
                <span class="flex-1 line-clamp-1">{{ item.title }}</span>
              </div>
            </template>
          </LoopScroll>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, onBeforeUnmount, computed } from 'vue'
import { getAssetsImage, countdown } from '@/utils/common'
import { getActiveRemainsApi, getStatsRemindTypesApi } from '@/api/station'
import { LoopScroll } from '@joyday/vue-loop-scroll'
import Container from '@/components/Container/Container.vue'

// 倒计时
const countdownTime = ref('00:00:00')
// 提醒分类数据
const remindTypesData = ref<any[]>([])
const remindTypesMap: Record<string, string> = {
  8: '事件提醒',
  7: '通知提醒',
  3: '巡检提醒',
}

// 提醒列表数据
const tipsListData = ref<any[]>([])

const countdownTimeComputed = computed(() => countdownTime.value.match(/\d{2}|:/g))

countdown({
  seconds: 60 * 60 * 24,
  format: 'HH:mm:ss',
  onTick: ({ formatted }) => {
    countdownTime.value = formatted
  },
  onFinish: () => {
    console.log('倒计时结束')
  },
})

const fetchTipsListData = async () => {
  const res = await getActiveRemainsApi()
  tipsListData.value = res
}

const fetchRemindTypesData = async () => {
  const res = await getStatsRemindTypesApi()
  console.log('res', res)
  remindTypesData.value = res
}

onBeforeMount(() => {
  fetchRemindTypesData()
  fetchTipsListData()
})
</script>

<style scoped lang="scss">
.scroll-item {
  @apply flex items-center gap-x-2.5 px-5 h-10 text-lg;
  font-family: 'AlibabaPuHuiTi', sans-serif;
  border-bottom: 1px solid rgba(216, 227, 238, 0.1);
}

:deep(.scroll-loop-track) {
  .scroll-loop-item:nth-child(even) > div {
    background-color: rgba(27, 130, 183, 0.12) !important;
  }
  .scroll-loop-item:nth-child(odd) > div {
    background-color: rgba(27, 130, 183, 0.22) !important;
  }
}
</style>
