<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-19
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-12
-->
<template>
  <button class="visibleAllBtn" @click="layoutStore.setShowTop(!layoutStore.showTop)">
    <img
      :src="
        layoutStore.showTop
          ? getAssetsImage('eye-off.png', 'layout')
          : getAssetsImage('eye.png', 'layout')
      "
      alt=""
      class="size-16"
    />
  </button>

  <!-- 顶部 -->
  <Transition name="fade">
    <div
      v-if="layoutStore.showTop"
      class="fixed top-42 left-1/2 -translate-x-1/2 flex items-center gap-x-5 text-lg font-['AlibabaPuHuiTi'] cursor-pointer"
      @contextmenu.prevent="openMenu"
    >
      <span>连续不间断播出时长：</span>
      <div class="flex items-center gap-x-2.5">
        <span
          class="flex items-center justify-center size-[50px] bg-black/50 font-['DS-Digital-Bold'] text-[50px] text-[#FFD631] border border-solid border-[#32DCFB]"
          v-for="(h, i) in playTime"
          :key="i"
        >
          {{ h }}
        </span>
      </div>
      <span>小时</span>
    </div>
  </Transition>

  <div class="overview">
    <!-- 左侧面板 -->
    <div :class="['left-container', leftContainerClass]">
      <slot name="left" />
    </div>

    <!-- 左侧按钮 -->
    <button
      :class="['btn', layoutStore.showLeftPanel ? 'left-btn' : 'left-btn-reverse']"
      @click="toggleLeftPanel"
    ></button>

    <!-- 右侧面板 -->
    <div :class="['right-container', rightContainerClass]">
      <RightContainer />
    </div>

    <!-- 右侧按钮 -->
    <button
      :class="['btn', layoutStore.showRightPanel ? 'right-btn' : 'right-btn-reverse']"
      @click="toggleRightPanel"
    ></button>
  </div>

  <ContextMenu ref="ContextMenuRef" :items="contextMenuItems" />
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onUnmounted, ref, useTemplateRef } from 'vue'
import { useLayoutStore } from '@/stores/layoutStore'
import { getAssetsImage } from '@/utils/common'
import RightContainer from './RightContainer.vue'
import ContextMenu from '../ContextMenu/ContextMenu.vue'
import { getBroadcastTimeApi, resetBroadcastTimeApi } from '@/api/station'

const layoutStore = useLayoutStore()

const playTime = ref('0')
const broadcastStartTime = ref<Date | null>(null)
let timer: number | null = null

const ContextMenuRef = useTemplateRef('ContextMenuRef')

// 重置播出时间
const handleResetBroadcastTime = async () => {
  try {
    const res = await resetBroadcastTimeApi()
    console.log('重置播出时间成功:', res)

    // 清理之前的定时器
    if (timer) {
      clearInterval(timer)
      timer = null
    }

    // 直接使用重置接口返回的时间
    broadcastStartTime.value = new Date(res)
    // 立即执行一次计算
    calculateBroadcastDuration()

    // 启动新的定时器
    timer = setInterval(calculateBroadcastDuration, 1000)
  } catch (error) {
    console.error('重置播出时间失败:', error)
  }
}

const contextMenuItems = [{ label: '重置', action: handleResetBroadcastTime }]

// 格式化时间差为字符串（小时）
const formatTimeDifference = (hours: number): string => {
  return Math.floor(hours).toString()
}

// 计算播出时长
const calculateBroadcastDuration = () => {
  if (!broadcastStartTime.value) return

  const now = new Date()
  const diffMs = now.getTime() - broadcastStartTime.value.getTime()
  const diffHours = diffMs / (1000 * 60 * 60)

  playTime.value = formatTimeDifference(diffHours)
}

// 获取播出开始时间
const fetchBroadcastTime = async () => {
  try {
    // 清理之前的定时器
    if (timer) {
      clearInterval(timer)
      timer = null
    }

    const res = await getBroadcastTimeApi()
    broadcastStartTime.value = new Date(res)
    // 立即执行一次
    calculateBroadcastDuration()

    timer = setInterval(calculateBroadcastDuration, 1000)
  } catch (error) {
    console.error('获取播出时间失败:', error)
  }
}

const openMenu = (e: any) => {
  ContextMenuRef.value?.show(e)
}

onBeforeMount(() => {
  fetchBroadcastTime()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

const toggleLeftPanel = () => {
  layoutStore.setShowLeftPanel(!layoutStore.showLeftPanel)
}
const toggleRightPanel = () => {
  layoutStore.setShowRightPanel(!layoutStore.showRightPanel)
}

const visibleBtnRight = computed(() => {
  return layoutStore.showRightPanel ? '644px' : '94px'
})

const leftContainerClass = computed(() => {
  return layoutStore.showLeftPanel
    ? 'translate-x-0 scale-100 opacity-100'
    : '-translate-x-120% scale-95 opacity-0'
})

const rightContainerClass = computed(() => {
  return layoutStore.showRightPanel
    ? 'translate-x-0 scale-100 opacity-100'
    : 'translate-x-120% scale-95 opacity-0'
})
</script>

<style scoped lang="scss">
.left-container,
.right-container {
  @apply fixed top-0 bottom-0 min-w-[612px] pt-[148px] z-120;

  transform-origin: right center;
  will-change: transform, opacity;
  transition:
    opacity 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    transform 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  &::before {
    content: '';
    @apply absolute top-0 w-120% h-full -z-1 pointer-events-none transition-all duration-500;
  }
}

.left-container {
  @apply left-0 pl-[70px];

  &::before {
    left: 0;
    background: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.9),
      rgba(0, 0, 0, 0.65) 65%,
      rgba(0, 0, 0, 0)
    );
  }
}

.right-container {
  @apply right-0 pr-[70px];

  &::before {
    right: 0;
    background: linear-gradient(
      to left,
      rgba(0, 0, 0, 0.9),
      rgba(0, 0, 0, 0.65) 65%,
      rgba(0, 0, 0, 0)
    );
  }
}

.btn {
  @apply fixed top-1/2 -translate-y-1/2 cursor-pointer bg-no-repeat bg-left-top bg-cover;
  width: 36px;
  height: 249px;
  will-change: left, right;
  transition:
    left 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    right 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.left-btn {
  left: 620px;
  background-image: url('@/assets/images/layout/arrow-left.png');
}

.left-btn-reverse {
  left: 40px;
  background-image: url('@/assets/images/layout/arrow-left-reverse.png');
}

.right-btn {
  right: 620px;
  background-image: url('@/assets/images/layout/arrow-right.png');
}

.right-btn-reverse {
  right: 46px;
  background-image: url('@/assets/images/layout/arrow-right-reverse.png');
}

.visibleAllBtn {
  @apply fixed top-42 cursor-pointer z-130;
  right: v-bind('visibleBtnRight');
  transition: right 0.5s ease;
}
</style>
