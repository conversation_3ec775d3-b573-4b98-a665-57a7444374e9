<template>
  <Container title="播出数据">
    <div class="h-95 pt-2 px-3">
      <!-- 上行播出数据 -->
      <div>
        <!-- 标题 -->
        <div class="flex items-center justify-between font-italic font-500 text-lg text-[#D1D6DF]">
          <div class="h-7.5 w-75">
            <span class="title inline-block">上行播出数据</span>
          </div>
          <div class="title">{{ broadcastData.name }}</div>
        </div>
        <!-- 内容 -->
        <div class="grid grid-cols-4 gap-x-9 gap-y-4 pt-4">
          <div class="flex flex-col" v-for="(item, index) in upPlayData" :key="index">
            <span class="text-lg font-400 text-white">{{ item.label }}</span>
            <span class="text-[26px] font-['PangMenZhengDao'] text-[#FFD631] -mt-1">
              {{ item.value }}
            </span>
            <img src="@/assets/images/overview/line.png" alt="" class="w-[102px] h-2 mt-1" />
          </div>
        </div>
      </div>
      <!-- 下行播出数据 -->
      <div class="mt-4">
        <!-- 标题 -->
        <div class="flex items-center justify-between font-italic font-500 text-lg text-[#D1D6DF]">
          <div class="h-7.5 w-75">
            <span class="title inline-block">下行播出数据</span>
          </div>
        </div>
        <!-- 内容 -->
        <div class="flex items-center justify-center gap-x-[114px]">
          <div class="flex items-center flex-col">
            <div class="size-24 bg-[#62DCFF]/10 rounded-full">
              <Echart :options="noiseOptions" />
            </div>
            <div class="text-white font-['AlibabaPuHuiTi'] text-lg">信噪比</div>
          </div>

          <div class="flex items-center flex-col">
            <div class="size-24 bg-[#62DCFF]/10 rounded-full">
              <Echart :options="berOptions" />
            </div>
            <div class="text-white font-['AlibabaPuHuiTi'] text-lg">误码率</div>
          </div>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed } from 'vue'
import { merge } from 'lodash-es'
import { getBroadcastDataApi } from '@/api/station'
import Container from '../Container/Container.vue'
import Echart from '../Echart/Echart.vue'

import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

const broadcastData = ref<any>({})

// 上行播出数据
const upPlayData = computed(() => {
  return [
    {
      label: '上行功率',
      value: broadcastData.value.power ?? '--',
    },
    {
      label: '方位角',
      value: broadcastData.value.azimuth ?? '--',
    },
    {
      label: '俯仰角',
      value: broadcastData.value.elevation ?? '--',
    },
    {
      label: '极化角',
      value: broadcastData.value.polar ?? '--',
    },
    {
      label: '信标电平',
      value: broadcastData.value.beacon ?? '--',
    },
    {
      label: '电平偏差',
      value: broadcastData.value.deviation ?? '--',
    },
    {
      label: '符号率',
      value: broadcastData.value.symbol ?? '--',
    },
    {
      label: '码率',
      value: broadcastData.value.bitrate ?? '--',
    },
  ]
})

const baseOptions: EChartsOption = {
  title: {
    show: true,
    left: 'center',
    top: '34%',
    textStyle: {
      fontWeight: 'bold',
      fontFamily: 'DINNextLTPro',
      fontSize: 29,
      color: '#50E0FF',
      rich: {
        percent: {
          fontWeight: 'bold',
          fontFamily: 'DINNextLTPro',
          fontSize: 14,
          color: '#50E0FF',
          verticalAlign: 'middle',
        },
      },
    },
  },
  xAxis: {
    show: false,
  },
  yAxis: {
    show: false,
  },
  series: [
    {
      radius: '88%',
      type: 'gauge',
      startAngle: 360,
      endAngle: 0,
      pointer: {
        show: false,
      },
      progress: {
        width: 8,
        show: true,
        overlap: true,
        roundCap: false,
        clip: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: [
        {
          value: 0,
          name: '',
          title: {
            show: false,
          },
          detail: {
            show: false,
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#1DA7C0' },
              { offset: 1, color: '#C5FF87' },
            ]),
          },
        },
        {
          value: 100,
          name: '',
          title: {
            show: false,
          },
          detail: {
            show: false,
          },
          itemStyle: {
            color: '#507190',
          },
        },
      ],
    },
  ],
}

// 信噪比
const noiseOptions = computed<EChartsOption>(() => {
  const res = merge({}, baseOptions, {
    title: {
      text: `${broadcastData.value.noise ?? '--'}{percent|%}`,
    },
    series: [
      {
        data: [
          {
            value: broadcastData.value.noise || 0,
            name: '',
            title: {
              show: false,
            },
            detail: {
              show: false,
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1DA7C0' },
                { offset: 1, color: '#C5FF87' },
              ]),
            },
          },
        ],
      },
    ],
  })
  return res
})

// 误码率
const berOptions = computed<EChartsOption>(() => {
  const res = merge({}, baseOptions, {
    title: {
      text: `${broadcastData.value.ber ?? '--'}{percent|%}`,
    },
    series: [
      {
        data: [
          {
            value: broadcastData.value.ber || 0,
            name: '',
            title: {
              show: false,
            },
            detail: {
              show: false,
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1DA7C0' },
                { offset: 1, color: '#C5FF87' },
              ]),
            },
          },
        ],
      },
    ],
  })
  return res
})

const fetchBroadcastData = async () => {
  const res = await getBroadcastDataApi()
  broadcastData.value = res
}

onBeforeMount(() => {
  fetchBroadcastData()
})
</script>

<style scoped lang="scss">
.title {
  @include text-gradual(#acddff, #fff);
}
</style>
