<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-06
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-06
-->
<template>
  <div class="uiTable">
    <div class="thead">
      <div
        class="head-item"
        v-for="head in columns"
        :key="head.key"
        :style="{ width: head.width + 'px' }"
      >
        {{ head.name }}
      </div>
    </div>
    <div class="tbody" :style="{maxHeight: bodyMaxHeight}">
      <div class="body-item" v-for="(item, index) in data" :key="index">
        <div
          class="td"
          v-for="head in columns"
          :key="head.key"
          :style="{ width: head.width + 'px' }"
        >
          <template v-if="$slots[head.key]">
            <slot :name="head.key" :scope="item"></slot>
          </template>
          <span v-else>{{ item[head.key] }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

const props = withDefaults(
  defineProps<{
    columns: {
      key: string
      name: string
      width?: number
    }[]
    data: Record<string, any>[]
    maxRow?: number
  }>(),
  {
    maxRow: 3,
  },
)

const bodyMaxHeight = computed(() => props.maxRow * 40 + 'px')
</script>

<style scoped lang="scss">
.uiTable {
  @apply w-max text-white text-14px bg-#002545;
  .thead {
    @apply flex h-40px;
    border-bottom: 1px solid rgba(255,255,255,0.74);
    background: radial-gradient(ellipse at top, #002545 0%, #042d52 100%);
    .head-item {
      @apply flex justify-center items-center overflow-hidden text-ellipsis whitespace-nowrap;
    }
  }
  .tbody {
    @apply overflow-auto;
    .body-item {
      @apply flex h-40px;
      border-bottom: 1px solid rgba(255,255,255,0.74);
      background: radial-gradient(circle, rgba(27, 130, 183, 0.5) 0%, rgba(35, 104, 162, 0) 100%);
      .td {
        @apply flex justify-center items-center w-full h-full overflow-hidden text-ellipsis whitespace-nowrap;
      }
    }
  }
}
</style>
