export interface LoopScrollProps {
  /** 数据源 */
  data?: any[]
  /** 每项高度，默认50px */
  itemHeight?: number
  /** 滚动速度，单位：px/s，默认50 */
  speed?: number
  /** 滚动方向，默认'up' */
  direction?: 'up' | 'down' | 'left' | 'right'
  /** 是否暂停滚动，默认false */
  paused?: boolean
  /** 鼠标悬停时是否暂停，默认true */
  pauseOnHover?: boolean
  /** 容器高度，默认'200px' */
  height?: string
  /** 容器宽度，默认'100%' */
  width?: string
  /** 缓冲区倍数，默认2（渲染可视区域的2倍数据） */
  bufferScale?: number
}

export interface AutoScrollProps {
  /** 滚动速度，单位：px/s，默认50 */
  speed?: number
  /** 滚动方向，默认'up' */
  direction?: 'up' | 'down' | 'left' | 'right'
  /** 是否暂停滚动，默认false */
  paused?: boolean
  /** 鼠标悬停时是否暂停，默认true */
  pauseOnHover?: boolean
  /** 容器高度，默认'200px' */
  height?: string
  /** 容器宽度，默认'100%' */
  width?: string
}

export interface ScrollInstance {
  /** 开始滚动 */
  start: () => void
  /** 停止滚动 */
  stop: () => void
  /** 重置位置 */
  reset: () => void
}

export interface VirtualItem {
  data: any
  originalIndex: number
}
