<template>
  <div 
    ref="scrollContainer" 
    class="loop-scroll-container"
    :style="containerStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div 
      ref="scrollContent" 
      class="scroll-content"
      :style="contentStyle"
    >
      <!-- 虚拟滚动渲染的可视区域内容 -->
      <div 
        v-for="(item, index) in visibleItems" 
        :key="`${item.originalIndex}-${index}`"
        class="scroll-item"
        :style="getItemStyle(index)"
      >
        <slot :item="item.data" :index="item.originalIndex"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'

interface VirtualItem {
  data: any
  originalIndex: number
}

interface Props {
  /** 数据源 */
  data?: any[]
  /** 每项高度，默认50px */
  itemHeight?: number
  /** 滚动速度，单位：px/s，默认50 */
  speed?: number
  /** 滚动方向，默认'up' */
  direction?: 'up' | 'down' | 'left' | 'right'
  /** 是否暂停滚动，默认false */
  paused?: boolean
  /** 鼠标悬停时是否暂停，默认true */
  pauseOnHover?: boolean
  /** 容器高度，默认'200px' */
  height?: string
  /** 容器宽度，默认'100%' */
  width?: string
  /** 缓冲区倍数，默认2（渲染可视区域的2倍数据） */
  bufferScale?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  itemHeight: 50,
  speed: 50,
  direction: 'up',
  paused: false,
  pauseOnHover: true,
  height: '200px',
  width: '100%',
  bufferScale: 2
})

const scrollContainer = ref<HTMLElement>()
const scrollContent = ref<HTMLElement>()
const animationId = ref<number>()
const isHovered = ref(false)
const scrollOffset = ref(0)

// 容器样式
const containerStyle = computed(() => ({
  height: props.height,
  width: props.width,
  overflow: 'hidden',
  position: 'relative' as const
}))

// 计算可视区域能显示的项目数量
const visibleCount = computed(() => {
  const containerSize = parseInt(props.height)
  return Math.ceil(containerSize / props.itemHeight) + 1
})

// 计算需要渲染的项目数量（包含缓冲区）
const renderCount = computed(() => {
  return Math.min(visibleCount.value * props.bufferScale, props.data.length)
})

// 计算当前应该显示的项目
const visibleItems = computed((): VirtualItem[] => {
  if (!props.data.length) return []
  
  const items: VirtualItem[] = []
  const totalHeight = props.data.length * props.itemHeight
  const containerHeight = parseInt(props.height)
  
  // 计算当前滚动位置对应的起始索引
  const normalizedOffset = ((scrollOffset.value % totalHeight) + totalHeight) % totalHeight
  const startIndex = Math.floor(normalizedOffset / props.itemHeight)
  
  // 渲染足够的项目来填充可视区域和缓冲区
  for (let i = 0; i < renderCount.value; i++) {
    const dataIndex = (startIndex + i) % props.data.length
    items.push({
      data: props.data[dataIndex],
      originalIndex: dataIndex
    })
  }
  
  return items
})

// 内容样式
const contentStyle = computed(() => {
  const isVertical = props.direction === 'up' || props.direction === 'down'
  return {
    display: 'flex',
    flexDirection: isVertical ? ('column' as const) : ('row' as const),
    transform: getTransform(),
    transition: 'none'
  }
})

// 获取变换样式
const getTransform = () => {
  const totalHeight = props.data.length * props.itemHeight
  const normalizedOffset = ((scrollOffset.value % totalHeight) + totalHeight) % totalHeight
  
  switch (props.direction) {
    case 'up':
      return `translateY(${-normalizedOffset}px)`
    case 'down':
      return `translateY(${normalizedOffset}px)`
    case 'left':
      return `translateX(${-normalizedOffset}px)`
    case 'right':
      return `translateX(${normalizedOffset}px)`
    default:
      return `translateY(${-normalizedOffset}px)`
  }
}

// 获取每个项目的样式
const getItemStyle = (index: number) => {
  const isVertical = props.direction === 'up' || props.direction === 'down'
  return {
    height: isVertical ? `${props.itemHeight}px` : 'auto',
    width: isVertical ? '100%' : `${props.itemHeight}px`,
    flexShrink: 0
  }
}

// 开始滚动动画
const startAnimation = () => {
  if (!props.data.length) return
  
  let lastTime = 0
  
  const animate = (currentTime: number) => {
    if (lastTime === 0) lastTime = currentTime
    
    const deltaTime = currentTime - lastTime
    lastTime = currentTime
    
    // 如果暂停或鼠标悬停时暂停
    if (props.paused || (props.pauseOnHover && isHovered.value)) {
      animationId.value = requestAnimationFrame(animate)
      return
    }
    
    // 计算移动距离
    const moveDistance = (props.speed * deltaTime) / 1000
    
    // 更新滚动偏移
    if (props.direction === 'up' || props.direction === 'left') {
      scrollOffset.value += moveDistance
    } else {
      scrollOffset.value -= moveDistance
    }
    
    animationId.value = requestAnimationFrame(animate)
  }
  
  animationId.value = requestAnimationFrame(animate)
}

// 停止动画
const stopAnimation = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = undefined
  }
}

// 鼠标事件处理
const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

// 监听props变化
watch(() => props.paused, (newVal) => {
  if (newVal) {
    stopAnimation()
  } else {
    startAnimation()
  }
})

watch(() => props.speed, () => {
  stopAnimation()
  startAnimation()
})

watch(() => props.data, () => {
  scrollOffset.value = 0
  stopAnimation()
  if (!props.paused) {
    nextTick(() => {
      startAnimation()
    })
  }
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    if (!props.paused && props.data.length) {
      startAnimation()
    }
  })
})

onUnmounted(() => {
  stopAnimation()
})

// 暴露方法给父组件
defineExpose({
  start: startAnimation,
  stop: stopAnimation,
  reset: () => {
    scrollOffset.value = 0
  }
})
</script>

<style scoped>
.loop-scroll-container {
  position: relative;
  overflow: hidden;
}

.scroll-content {
  will-change: transform;
}

.scroll-item {
  flex-shrink: 0;
}
</style>
