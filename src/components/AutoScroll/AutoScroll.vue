<template>
  <div
    ref="scrollContainer"
    class="loop-scroll-container"
    :style="containerStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div ref="scrollContent" class="scroll-content" :style="contentStyle">
      <!-- 虚拟滚动渲染的可视区域内容 -->
      <div
        v-for="(item, index) in visibleItems"
        :key="`${item.originalIndex}-${index}`"
        class="scroll-item"
        :style="getItemStyle(index)"
      >
        <slot :item="item.data" :index="item.originalIndex"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

interface Props {
  /** 滚动速度，单位：px/s，默认50 */
  speed?: number
  /** 滚动方向，默认'up' */
  direction?: 'up' | 'down' | 'left' | 'right'
  /** 是否暂停滚动，默认false */
  paused?: boolean
  /** 鼠标悬停时是否暂停，默认true */
  pauseOnHover?: boolean
  /** 容器高度，默认'200px' */
  height?: string
  /** 容器宽度，默认'100%' */
  width?: string
}

const props = withDefaults(defineProps<Props>(), {
  speed: 50,
  direction: 'up',
  paused: false,
  pauseOnHover: true,
  height: '200px',
  width: '100%',
})

const scrollContainer = ref<HTMLElement>()
const scrollContent = ref<HTMLElement>()
const animationId = ref<number>()
const isHovered = ref(false)
const currentTransform = ref(0)

// 容器样式
const containerStyle = computed(() => ({
  height: props.height,
  width: props.width,
  overflow: 'hidden',
  position: 'relative' as const,
}))

// 内容样式
const contentStyle = computed(() => {
  const isVertical = props.direction === 'up' || props.direction === 'down'
  return {
    display: 'flex',
    flexDirection: isVertical ? 'column' : 'row',
    transform: getTransform(),
    transition: 'none',
  }
})

// 获取变换样式
const getTransform = () => {
  switch (props.direction) {
    case 'up':
      return `translateY(${currentTransform.value}px)`
    case 'down':
      return `translateY(${-currentTransform.value}px)`
    case 'left':
      return `translateX(${currentTransform.value}px)`
    case 'right':
      return `translateX(${-currentTransform.value}px)`
    default:
      return `translateY(${currentTransform.value}px)`
  }
}

// 开始滚动动画
const startAnimation = () => {
  if (!scrollContainer.value || !scrollContent.value) return

  const container = scrollContainer.value
  const content = scrollContent.value
  const isVertical = props.direction === 'up' || props.direction === 'down'

  // 获取单个内容项的尺寸
  const scrollItem = content.querySelector('.scroll-item') as HTMLElement
  if (!scrollItem) return

  const itemSize = isVertical ? scrollItem.offsetHeight : scrollItem.offsetWidth

  let lastTime = 0

  const animate = (currentTime: number) => {
    if (lastTime === 0) lastTime = currentTime

    const deltaTime = currentTime - lastTime
    lastTime = currentTime

    // 如果暂停或鼠标悬停时暂停
    if (props.paused || (props.pauseOnHover && isHovered.value)) {
      animationId.value = requestAnimationFrame(animate)
      return
    }

    // 计算移动距离
    const moveDistance = (props.speed * deltaTime) / 1000

    // 更新位置
    if (props.direction === 'up' || props.direction === 'left') {
      currentTransform.value -= moveDistance
      // 当移动距离达到一个完整项目的高度/宽度时，重置位置
      if (Math.abs(currentTransform.value) >= itemSize) {
        currentTransform.value = 0
      }
    } else {
      currentTransform.value += moveDistance
      if (currentTransform.value >= itemSize) {
        currentTransform.value = 0
      }
    }

    animationId.value = requestAnimationFrame(animate)
  }

  animationId.value = requestAnimationFrame(animate)
}

// 停止动画
const stopAnimation = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = undefined
  }
}

// 鼠标事件处理
const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

// 监听props变化
watch(
  () => props.paused,
  (newVal) => {
    if (newVal) {
      stopAnimation()
    } else {
      startAnimation()
    }
  },
)

watch(
  () => props.speed,
  () => {
    // 速度变化时重新开始动画
    stopAnimation()
    startAnimation()
  },
)

onMounted(() => {
  // 确保DOM渲染完成后开始动画
  setTimeout(() => {
    if (!props.paused) {
      startAnimation()
    }
  }, 100)
})

onUnmounted(() => {
  stopAnimation()
})

// 暴露方法给父组件
defineExpose({
  start: startAnimation,
  stop: stopAnimation,
  reset: () => {
    currentTransform.value = 0
  },
})
</script>

<style scoped>
.auto-scroll-container {
  position: relative;
  overflow: hidden;
}

.scroll-content {
  will-change: transform;
}

.scroll-item {
  flex-shrink: 0;
}
</style>
