<template>
  <div class="example-container">
    <h2>AutoScroll 组件使用示例</h2>
    
    <!-- 基础向上滚动 -->
    <div class="demo-section">
      <h3>基础向上滚动</h3>
      <AutoScroll :speed="30" height="200px">
        <div class="scroll-item" v-for="item in items" :key="item.id">
          <div class="item-content">
            <span class="item-title">{{ item.title }}</span>
            <span class="item-desc">{{ item.description }}</span>
          </div>
        </div>
      </AutoScroll>
    </div>

    <!-- 快速滚动 -->
    <div class="demo-section">
      <h3>快速滚动 (速度: 80px/s)</h3>
      <AutoScroll :speed="80" height="150px">
        <div class="scroll-item" v-for="item in items.slice(0, 5)" :key="item.id">
          <div class="item-content simple">
            {{ item.title }}
          </div>
        </div>
      </AutoScroll>
    </div>

    <!-- 控制按钮 -->
    <div class="demo-section">
      <h3>可控制的滚动</h3>
      <div class="controls">
        <button @click="togglePause">{{ isPaused ? '开始' : '暂停' }}</button>
        <button @click="changeSpeed">切换速度 (当前: {{ currentSpeed }}px/s)</button>
      </div>
      <AutoScroll 
        :speed="currentSpeed" 
        :paused="isPaused" 
        height="180px"
        ref="controllableScroll"
      >
        <div class="scroll-item" v-for="item in items.slice(0, 6)" :key="item.id">
          <div class="item-content">
            <span class="item-title">{{ item.title }}</span>
            <span class="item-time">{{ item.time }}</span>
          </div>
        </div>
      </AutoScroll>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AutoScroll from './AutoScroll.vue'

const isPaused = ref(false)
const currentSpeed = ref(50)
const controllableScroll = ref()

const items = ref([
  { id: 1, title: '系统通知', description: '系统将于今晚进行维护升级', time: '10:30' },
  { id: 2, title: '安全提醒', description: '请及时更新您的密码', time: '09:15' },
  { id: 3, title: '功能更新', description: '新增数据导出功能', time: '08:45' },
  { id: 4, title: '活动通知', description: '智慧园区建设研讨会即将开始', time: '08:00' },
  { id: 5, title: '设备状态', description: '监控设备运行正常', time: '07:30' },
  { id: 6, title: '数据报告', description: '昨日访客统计已生成', time: '07:00' },
  { id: 7, title: '温度预警', description: '机房温度略高，请注意', time: '06:45' },
  { id: 8, title: '网络状态', description: '网络连接稳定', time: '06:30' }
])

const togglePause = () => {
  isPaused.value = !isPaused.value
}

const changeSpeed = () => {
  const speeds = [30, 50, 80, 120]
  const currentIndex = speeds.indexOf(currentSpeed.value)
  currentSpeed.value = speeds[(currentIndex + 1) % speeds.length]
}
</script>

<style scoped>
.example-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: #f9f9f9;
}

.demo-section h3 {
  margin-top: 0;
  color: #333;
}

.scroll-item {
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
}

.scroll-item:last-child {
  border-bottom: none;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.item-content.simple {
  justify-content: center;
  font-weight: 500;
  color: #2c3e50;
}

.item-title {
  font-weight: 600;
  color: #2c3e50;
}

.item-desc {
  color: #7f8c8d;
  font-size: 14px;
}

.item-time {
  color: #95a5a6;
  font-size: 12px;
}

.controls {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.controls button {
  padding: 8px 16px;
  border: 1px solid #3498db;
  background: #3498db;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.controls button:hover {
  background: #2980b9;
}
</style>
