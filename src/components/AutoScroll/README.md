# AutoScroll 无限自动滚动组件

一个支持多方向、可控制速度的无限自动滚动Vue组件。

## 特性

- ✅ 支持四个方向滚动：上、下、左、右
- ✅ 可自定义滚动速度
- ✅ 支持暂停/恢复滚动
- ✅ 鼠标悬停暂停功能
- ✅ 无缝循环滚动
- ✅ TypeScript 支持
- ✅ 响应式设计

## 基础用法

```vue
<template>
  <AutoScroll :speed="50" height="200px">
    <div v-for="item in items" :key="item.id">
      {{ item.content }}
    </div>
  </AutoScroll>
</template>

<script setup>
import AutoScroll from '@/components/AutoScroll'

const items = ref([
  { id: 1, content: '内容1' },
  { id: 2, content: '内容2' },
  { id: 3, content: '内容3' }
])
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| speed | number | 50 | 滚动速度，单位：px/s |
| direction | 'up' \| 'down' \| 'left' \| 'right' | 'up' | 滚动方向 |
| paused | boolean | false | 是否暂停滚动 |
| pauseOnHover | boolean | true | 鼠标悬停时是否暂停 |
| height | string | '200px' | 容器高度 |
| width | string | '100%' | 容器宽度 |

## 方法

通过 ref 可以调用以下方法：

| 方法名 | 说明 |
|--------|------|
| start() | 开始滚动 |
| stop() | 停止滚动 |
| reset() | 重置滚动位置 |

## 使用示例

### 基础向上滚动

```vue
<AutoScroll :speed="30" height="200px">
  <div class="item" v-for="item in items" :key="item.id">
    {{ item.title }}
  </div>
</AutoScroll>
```

### 可控制的滚动

```vue
<template>
  <div>
    <button @click="togglePause">{{ isPaused ? '开始' : '暂停' }}</button>
    <AutoScroll 
      :speed="60" 
      :paused="isPaused" 
      height="180px"
      ref="scrollRef"
    >
      <div v-for="item in items" :key="item.id">
        {{ item.content }}
      </div>
    </AutoScroll>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AutoScroll from '@/components/AutoScroll'

const isPaused = ref(false)
const scrollRef = ref()

const togglePause = () => {
  isPaused.value = !isPaused.value
}

// 手动控制
const resetScroll = () => {
  scrollRef.value?.reset()
}
</script>
```

### 水平滚动

```vue
<AutoScroll 
  direction="left" 
  :speed="40" 
  height="60px" 
  width="300px"
>
  <div class="horizontal-item" v-for="item in items" :key="item.id">
    {{ item.title }}
  </div>
</AutoScroll>
```

## 样式自定义

组件提供了基础的滚动容器，内容样式完全由使用者控制：

```vue
<AutoScroll height="250px">
  <div class="custom-item" v-for="item in items" :key="item.id">
    <div class="item-header">{{ item.title }}</div>
    <div class="item-content">{{ item.description }}</div>
    <div class="item-time">{{ item.time }}</div>
  </div>
</AutoScroll>

<style scoped>
.custom-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
  background: white;
  margin-bottom: 8px;
  border-radius: 6px;
}

.item-header {
  font-weight: bold;
  color: #333;
}

.item-content {
  color: #666;
  margin: 4px 0;
}

.item-time {
  font-size: 12px;
  color: #999;
}
</style>
```

## 注意事项

1. 确保滚动内容有足够的高度/宽度来实现滚动效果
2. 组件会自动克隆内容来实现无缝循环
3. 建议为滚动项设置固定的尺寸以获得最佳效果
4. 在大量数据时，考虑使用虚拟滚动来优化性能
