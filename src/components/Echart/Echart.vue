<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2024-12-04
 * @LastEditors: 余承
 * @LastEditTime: 2025-06-22
-->
<template>
  <div w-full h-full ref="elEcharts" />
</template>
<script setup lang="ts">
import { shallowRef, onMounted, watch, useTemplateRef, onUnmounted, nextTick } from 'vue'
import { useEcharts } from '@/hooks/useEcharts'
import type { EChartsOption } from 'echarts'

const props = withDefaults(
  defineProps<{
    options: EChartsOption
    immediate?: boolean
  }>(),
  {
    immediate: true,
  },
)
const elEcharts = useTemplateRef('elEcharts')
const currentOptions = shallowRef(props.options)
const { chart, setOptions, initCharts, getInstance } = useEcharts(elEcharts, currentOptions.value)

watch(
  () => props.options,
  (nVal) => {
    setOptions({ ...nVal })
  },
  { deep: true },
)

onMounted(() => {
  initCharts()
})

defineExpose({
  chart,
  getInstance,
})
</script>
