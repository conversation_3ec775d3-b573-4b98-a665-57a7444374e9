<!--
 * @Description: 3D场景查看器
 * @Author: yucheng
 * @Date: 2025-06-19
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-22
-->

<template>
  <div id="viewer" ref="viewerContainer" v-show="showScene"></div>
  <Transition name="fade-mask">
    <div
      class="fixed left-0 top-0 w-full h-full bg-#001a33 flex items-center justify-center z-100"
      v-if="!threeStore.sceneLoaded"
    >
      <div class="loading-container text-center">
        <div class="loading-spinner"></div>
        <div class="text-#00e6ff text-xl font-light mt-2">
          场景加载中<span class="dot">...</span>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref, watch } from 'vue'
import { useTemplateRef } from 'vue'
import { initViewer, viewer } from '@/three/viewer'
import { ResourceManager } from '@/three/resource'
import * as THREE from 'three'
import { performanceMonitor } from '@/three/utils'
import { useRoute } from 'vue-router'
import { useThreeStore } from '@/stores/threeStore'

const threeStore = useThreeStore()

const showScene = ref(true)

const viewerContainer = useTemplateRef('viewerContainer')

const loadModel = async () => {
  const resourceManager = ResourceManager.getInstance()
  const models = [
    {
      url: 'models/发射台ys.glb',
      shadow: true,
    },
    {
      url: 'models/建筑地形加机房ys.glb',
      shadow: true,
    },
    {
      url: 'models/周边环境ys.glb',
      shadow: false,
    },
  ]
  try {
    const gltfs = await Promise.all(models.map((model) => resourceManager.loadGLTF(model.url)))
    gltfs.forEach((gltf, index) => {
      gltf.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.receiveShadow = models[index].shadow
          child.castShadow = models[index].shadow
        }
      })
    })
    viewer.scene.add(...gltfs)
    const boli = viewer.scene.getObjectByName('GDYQ-ZT-JZ-0001_1')
    if (boli && boli instanceof THREE.Mesh) {
      boli.material.opacity = 0.95
      boli.material.transparent = true
    }
  } catch (error) {
    console.error(error)
  } finally {
    threeStore.setSceneLoaded(true)
  }
}

const initScene = () => {
  nextTick(async () => {
    if (viewerContainer.value) {
      console.time('初始化场景')
      await initViewer(viewerContainer.value)
      console.timeEnd('初始化场景')
      console.time('加载模型')
      await loadModel()
      console.timeEnd('加载模型')
      viewer.skybox.setSkyBox({
        name: 'day',
        url: 'skybox/skybox-day.exr',
      })
    }
  })
}

const route = useRoute()
watch(route, (e) => {
  showScene.value = (e.meta.threeScene || false) as boolean
})

let isPerformanceMonitor = false
onMounted(() => {
  initScene()
  window.addEventListener('keydown', (e) => {
    if (['p', 'P'].includes(e.key) && e.altKey) {
      isPerformanceMonitor = !isPerformanceMonitor
      if (isPerformanceMonitor) {
        performanceMonitor.initialize()
      } else {
        performanceMonitor.dispose()
      }
    }
  })
})
</script>

<style lang="scss" scoped>
#viewer {
  @apply fixed top-0 left-0 w-full h-full bg-black;
  // background-color: rgba(255, 255, 255, 0.5);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 230, 255, 0.2);
  border-radius: 50%;
  border-top-color: #00e6ff;
  box-shadow: 0 0 15px rgba(0, 230, 255, 0.3);
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.fade-mask-enter-active,
.fade-mask-leave-active {
  transition: opacity 0.3s ease;
}

.fade-mask-enter-from,
.fade-mask-leave-to {
  opacity: 0;
}

.dot {
  display: inline-block;
  height: 1em;
  line-height: 1;
  vertical-align: -0.25em;
  overflow: hidden;
}

.dot::before {
  display: block;
  content: '...\A..\A.';
  white-space: pre-wrap;
  animation: dot 3s infinite step-start both;
}

@keyframes dot {
  33% {
    transform: translateY(-2em);
  }

  66% {
    transform: translateY(-1em);
  }
}
</style>
