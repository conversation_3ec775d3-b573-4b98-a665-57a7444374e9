<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-19
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-11
-->
<template>
  <div class="layout">
    <!-- <three-map />
    <cesium-map /> -->
    <img src="@/assets/images/layout/border.png" alt="" class="border-img" />
    <div class="page">
      <Header />

      <!-- 左侧场景监测 -->
      <Transition name="fade">
        <div
          v-show="layoutStore.showTop"
          class="scene-box"
          :style="{ backgroundImage: `url(${getAssetsImage('场景监测.png', 'overview')})` }"
        >
          <p class="scene-text">场景监测</p>
        </div>
      </Transition>

      <!-- 右侧操作区域 -->
      <Transition name="fade">
        <div
          v-show="layoutStore.showTop"
          class="fixed w-fit top-[90px] right-[70px] flex items-center gap-x-8 justify-end z-99999"
        >
          <img src="@/assets/images/overview/消息.png" alt="" class="size-10 cursor-pointer" />
          <div class="w-[3px] h-9 bg-[#9EC5E7]"></div>
          <img
            src="@/assets/images/overview/个人中心.png"
            alt=""
            class="size-10 cursor-pointer"
            @click="showLogoutModal = true"
          />
        </div>
      </Transition>

      <PageWrapper>
        <template #left>
          <router-view> </router-view>
        </template>
      </PageWrapper>
    </div>
    <Menu />

    <!-- 退出登录 -->
    <Modal v-model:visible="showLogoutModal" />
  </div>
</template>

<script setup lang="ts">
import ThreeMap from '@/components/threeMap/threeMap.vue'
import Header from '@/components/layout/header.vue'
import CesiumMap from '@/components/cesiumMap/cesiumMap.vue'
import Menu from '@/components/layout/menu.vue'
import PageWrapper from '@/components/pageWrapper/pageWrapper.vue'
import Modal from '@/components/Modal/Modal.vue'
import { useMessage } from 'naive-ui'
import { getAssetsImage } from '@/utils/common'
import { useLayoutStore } from '@/stores/layoutStore'
import { ref } from 'vue'

const showLogoutModal = ref(false)

const message = useMessage()
window.$message = message

const layoutStore = useLayoutStore()
</script>

<style scoped lang="scss">
.layout {
  @apply relative w-full h-full;

  .border-img {
    @apply absolute top-0 left-0 w-full h-full pointer-events-none z-150;
  }

  .page {
    @apply relative z-120;
  }
}

.scene-box {
  @apply fixed top-[90px] left-[70px] w-[175px] pb-2.5 h-8 bg-no-repeat bg-center bg-cover text-[26px] font-['JiangChengXieHei-700W'] mb-7.5 z-99999;
}

.scene-text {
  @include text-gradual(#ffe8c7, #fff);
  @apply absolute bottom-3.5 left-1/2 -translate-x-1/2 text-nowrap text-center text-white cursor-pointer;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
