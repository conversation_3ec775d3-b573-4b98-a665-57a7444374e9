<template>
  <div class="header">
    <div class="header-left">
      <img src="@/assets/images/layout/header-bg.png" class="header-bg" />
      <div class="header-title">智慧运维平台系统</div>
    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped lang="scss">
.header {
  @apply absolute top-0 left-50% -translate-x-1/2 flex justify-center;
  width: 1168px;
  height: 134px;

  .header-bg {
    @apply absolute top-0 left-0 w-full h-full -z-1;
  }

  .header-title {
    @apply text-white text-56px pt-5;
    font-family: 'YouSheBiaoTiHei';
    letter-spacing: 10px;
    text-shadow: 0px 3px 4px rgba(17, 20, 22, 0.31);
  }
}
</style>
