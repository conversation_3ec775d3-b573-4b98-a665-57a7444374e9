<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-19
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-21
-->
<template>
  <div class="fixed w-fit bottom-8 left-1/2 -translate-x-1/2 h-40 z-120">
    <button
      :class="[layoutStore.showBottomMenu ? 'menu-btn' : 'menu-btn-reverse', 'btn']"
      @click="toggleBottomMenu"
    ></button>

    <Transition name="menu">
      <div
        v-show="layoutStore.showBottomMenu"
        class="flex items-center justify-center gap-x-[145px] z-120"
      >
        <div
          v-for="(item, index) in footerList"
          :key="index"
          @click="handleClick(item)"
          class="relative size-40 bg-contain bg-center bg-no-repeat flex items-center justify-center cursor-pointer"
          :style="{
            backgroundImage: `url(${item.path === activeMenu || (!item.path && activeMenu === DATA_CENTER_LAYOUT_PATH) ? item.activeIcon : item.icon})`,
          }"
        >
          <span class="font-['JiangChengXieHei-700W'] text-[26px] text-[#C5DFEF] text-center mt-10">
            {{ item.label }}
          </span>

          <div
            v-if="!item.path"
            class="dropdown absolute -top-4 left-1/2 -translate-x-1/2 -translate-y-full h-auto p-4 rounded-md space-y-3 transition-all duration-200 transform-origin-bottom-center"
            :class="dropdownActive ? 'scale-100' : 'scale-0'"
          >
            <div
              v-for="d in options"
              :key="d.value"
              class="dropdown-item"
              :class="dropdownItemActiveIndex === d.value ? 'active' : ''"
              @click.stop="handleDropdownItemClick(item, d)"
            >
              {{ d.label }}
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layoutStore'
import { navigate } from '@/three/navigate'
import { getAssetsImage } from '@/utils/common'
import { computed, onMounted, ref, useTemplateRef, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { onClickOutside } from '@vueuse/core'
import { flyToSignal, flyToUps } from '@/views/data-center-layout'

const DATA_CENTER_LAYOUT_PATH = '/data-center-layout'

const route = useRoute()
const router = useRouter()

const dropdownActive = ref(false)
const dropdownItemActiveIndex = ref()

const activeMenu = computed(() => {
  return route.path
})

const footerList = ref([
  {
    label: '地球站全景',
    icon: getAssetsImage('地球站全景.png', 'layout'),
    activeIcon: getAssetsImage('地球站全景-active.png', 'layout'),
    path: '/overview',
  },
  {
    label: '节目覆盖',
    icon: getAssetsImage('节目覆盖.png', 'layout'),
    activeIcon: getAssetsImage('节目覆盖-active.png', 'layout'),
    path: '/program-coverage',
  },
  {
    label: '机房布局',
    icon: getAssetsImage('机房布局.png', 'layout'),
    activeIcon: getAssetsImage('机房布局-active.png', 'layout'),
  },
  {
    label: '系统拓扑',
    icon: getAssetsImage('系统拓扑.png', 'layout'),
    activeIcon: getAssetsImage('系统拓扑-active.png', 'layout'),
    path: '/system-topology',
  },
])

const options = [
  {
    label: '信号机房',
    value: 0,
  },
  {
    label: 'UPS机房',
    value: 1,
  },
]

const layoutStore = useLayoutStore()
const handleDropdownItemClick = (item: any, option: any) => {
  item.label = option.label
  dropdownItemActiveIndex.value = option.value
  dropdownActive.value = false
  router.push(DATA_CENTER_LAYOUT_PATH)
  layoutStore.setDataCenterRoom(item.label)
  if (item.label === '信号机房') {
    flyToSignal()
  } else if (item.label === 'UPS机房') {
    flyToUps()
  }
}

const handleClick = (item: any) => {
  if (!item.path) {
    dropdownActive.value = !dropdownActive.value
    return
  }

  if (dropdownActive.value) {
    updateDataCenterLayoutLabel()
  }

  if (item.path === activeMenu.value) return

  dropdownItemActiveIndex.value = undefined
  router.push(item.path)
}

const updateDataCenterLayoutLabel = () => {
  const dataCenterLayout = footerList.value.find((item) => !item.path)
  const selectedOption = options.find((option) => option.value === dropdownItemActiveIndex.value)

  if (dataCenterLayout && selectedOption) {
    dataCenterLayout.label = selectedOption.label
  } else {
    dropdownActive.value = false
  }
}

const toggleBottomMenu = () => {
  layoutStore.setShowBottomMenu(!layoutStore.showBottomMenu)
}

watch(activeMenu, (newVal) => {
  const dataCenterLayout = footerList.value.find((item) => !item.path)!
  if (newVal !== DATA_CENTER_LAYOUT_PATH) {
    dropdownActive.value = false
    dataCenterLayout.label = '机房布局'
    layoutStore.setDataCenterRoom('')
  } else {
    if (dropdownActive.value) {
      dataCenterLayout.label = options.find(
        (item) => item.value === dropdownItemActiveIndex.value,
      )!.label
    }
  }
})

onMounted(() => {
  const dropdownEL = document.querySelector('.dropdown') as HTMLDivElement
  if (dropdownEL) {
    onClickOutside(dropdownEL, () => {
      dropdownActive.value = false
    })
  }
})
</script>

<style lang="scss" scoped>
.dropdown {
  background: rgba(0, 62, 116, 0.85);
  border: 2px solid #0d65b1;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 20px;
  min-width: 160px;
  height: 46px;
  background-color: #055091;
  box-shadow: inset 0px 0px 8.3px 0px rgba(78, 210, 255, 0.5);
  font-family: 'AlibabaPuHuiTi';
  font-size: 18px;
  color: #ffffff;
  border: none;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: #006cc8;
    box-shadow: inset 0 0 15px 0px rgba(107, 225, 255, 1);
  }

  &.active {
    background-color: #006cc8;
    box-shadow: inset 0 0 15px 0px rgba(107, 225, 255, 1);
  }
}

.btn {
  @apply absolute left-1/2 -translate-x-1/2 -translate-y-full cursor-pointer bg-no-repeat bg-left-top bg-cover;
  width: 249px;
  height: 36px;
  transition: top 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.menu-btn {
  top: -12px;
  background-image: url('@/assets/images/layout/arrow-down.png');
}

.menu-btn-reverse {
  top: calc(100% - 24px);
  background-image: url('@/assets/images/layout/arrow-down-reverse.png');
}

.menu-enter-from,
.menu-leave-to {
  opacity: 0;
  transform: translateY(100%);
}

.menu-enter-active,
.menu-leave-active {
  transition:
    opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
</style>
