<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2024-12-31
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-18
-->
<template>
  <i :class="['iconfont', iconClass]" :style="style"></i>
</template>

<script lang="ts" setup>
import { computed, type StyleValue } from 'vue'

const props = defineProps<{
  name: string
  color?: string
  size?: string
  [key: string]: any // 允许任意其他属性传递
}>()

const style = computed(() => {
  const styleObj: StyleValue = {}
  if (props.color !== 'inherit') {
    styleObj['color'] = props.color
  }
  if (props.size !== 'inherit') {
    styleObj['fontSize'] = props.size
  }
  return styleObj
})

const iconClass = computed(() => {
  return `icon-${props.name}`
})
</script>

<style scoped lang="scss"></style>
