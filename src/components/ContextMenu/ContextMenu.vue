<template>
  <teleport to=".screen-wrapper">
    <transition name="context-menu-fade">
      <div
        v-show="visible"
        class="context-menu"
        :style="{ top: y + 'px', left: x + 'px' }"
        @click.stop
      >
        <ul>
          <li v-for="(item, index) in items" :key="index" @click="handleClick(item)">
            {{ item.label }}
          </li>
        </ul>
      </div>
    </transition>
  </teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

defineProps<{
  items: { label: string; action: () => void }[]
}>()

const visible = ref(false)
const x = ref(0)
const y = ref(0)

const show = (e: any) => {
  e.preventDefault()

  // 获取缩放比例和边距信息
  const getScaleInfo = () => {
    const screenWrapper = document.querySelector('.screen-wrapper') as HTMLElement
    if (!screenWrapper) return { scale: 1, offsetX: 0, offsetY: 0 }

    const computedStyle = getComputedStyle(screenWrapper)
    const transform = computedStyle.transform
    let scale = 1

    if (transform !== 'none') {
      const matrix = transform.match(/matrix\(([^)]+)\)/)
      if (matrix) {
        const values = matrix[1].split(',').map((v) => parseFloat(v.trim()))
        scale = values[0] // scaleX
      } else {
        const scaleMatch = transform.match(/scale\(([^)]+)\)/)
        if (scaleMatch) {
          scale = parseFloat(scaleMatch[1])
        }
      }
    }

    // 计算缩放后的偏移量
    const rect = screenWrapper.getBoundingClientRect()
    const offsetX = rect.left
    const offsetY = rect.top

    return { scale, offsetX, offsetY }
  }

  const { scale, offsetX, offsetY } = getScaleInfo()

  // 将鼠标坐标转换为缩放容器内的坐标
  let posX = (e.clientX - offsetX) / scale
  let posY = (e.clientY - offsetY) / scale

  // 获取缩放容器的实际尺寸（设计尺寸）
  const containerWidth = 2560
  const containerHeight = 1440

  // 菜单预估尺寸
  const menuWidth = 160
  const menuHeight = 200

  // 防止菜单超出边界（基于设计尺寸）
  if (posX + menuWidth > containerWidth) {
    posX = containerWidth - menuWidth - 10
  }

  if (posY + menuHeight > containerHeight) {
    posY = containerHeight - menuHeight - 10
  }

  // 防止菜单超出左边界和上边界
  posX = Math.max(10, posX)
  posY = Math.max(10, posY)

  x.value = posX
  y.value = posY
  visible.value = true
}

const hide = () => {
  visible.value = false
}

const handleClick = (item: { label: string; action: () => void }) => {
  if (item?.action) item.action()
  hide()
}

// 监听点击空白关闭
onMounted(() => {
  document.addEventListener('click', hide)
})
onBeforeUnmount(() => {
  document.removeEventListener('click', hide)
})

// 对外暴露 show 方法
defineExpose({ show, hide })
</script>

<style scoped lang="scss">
.context-menu {
  position: fixed;
  background: rgba(0, 62, 116, 0.95);
  border: 1px solid #0d65b1;
  border-radius: 4px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(117, 209, 254, 0.2);
  min-width: 160px;
  z-index: 10000;
  backdrop-filter: blur(8px);
  font-family: 'AlibabaPuHuiTi', sans-serif;

  // 科技感装饰线条
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 8px;
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, #75d1fe 0%, rgba(117, 209, 254, 0) 100%);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    right: 8px;
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, rgba(117, 209, 254, 0) 0%, #75d1fe 100%);
  }
}

.context-menu ul {
  list-style: none;
  margin: 0;
  padding: 4px 0;
}

.context-menu li {
  position: relative;
  padding: 10px 16px;
  font-size: 14px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 2px solid transparent;

  &:hover {
    background: rgba(0, 124, 255, 0.3);
    border-left-color: #00c0f3;
    color: #ffffff;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 60%;
      background: linear-gradient(180deg, #00c0f3 0%, #75d1fe 100%);
      border-radius: 0 2px 2px 0;
    }
  }

  &:active {
    background: rgba(0, 124, 255, 0.5);
    transform: translateX(1px);
  }

  &:first-child {
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }

  &:last-child {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
  }
}

// Vue transition 动画
.context-menu-fade-enter-active,
.context-menu-fade-leave-active {
  transition: all 0.15s ease-out;
  transform-origin: top left;
}

.context-menu-fade-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(-4px);
}

.context-menu-fade-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-4px);
}
</style>
