<template>
  <Teleport to=".screen-wrapper">
    <Transition name="modal">
      <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
        <div class="modal-container" @click.stop>
          <div class="line"></div>
          <div class="line"></div>
          <div class="line"></div>
          <div class="line"></div>

          <!-- Header -->
          <div class="modal-header">
            <span class="modal-title">
              {{ title }}
            </span>
            <span class="modal-close" @click="handleCloseClick">×</span>
          </div>

          <!-- Body -->
          <div class="modal-body">
            <slot>
              <div class="modal-content">是否退出登录？</div>
            </slot>
          </div>

          <div class="h-[2px]" style="background: linear-gradient(90deg, #9dcae6 0%)"></div>

          <!-- Footer -->
          <div class="modal-footer">
            <template v-if="!$slots.footer">
              <button class="btn btn-cancel" v-if="showCancel" @click="handleCancelClick">{{ cancelText }}</button>
              <button class="btn btn-confirm" v-if="showConfirm" @click="handleConfirmClick">{{ confirmText }}</button>
            </template>
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">

const props = withDefaults(defineProps<{
  title?: string
  showCancel?: boolean
  cancelText?: string
  showConfirm?: boolean
  confirmText?: string
}>(), {
  showCancel: true,
  showConfirm: true,
  cancelText: '取消',
  confirmText: '确认'
})

const emit = defineEmits(['close', 'confirm', 'cancel'])

const visible = defineModel<boolean>('visible', { default: false })

const handleOverlayClick = () => {
  visible.value = false
  emit('close')
}

const handleCloseClick = () => {
  visible.value = false
  emit('close')
}

const handleCancelClick = () => {
  emit('cancel')
}

const handleConfirmClick = () => {
  emit('confirm')
}
</script>

<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  position: relative;
  padding: 24px;
  max-width: 80vw;
  max-height: 80vh;
  min-width: 380px;
  min-height: 204px;
  background-color: rgba(0, 62, 116, 0.85);
  border: 2px solid #0d65b1;

  .line {
    position: absolute;
    width: 18px;
    height: 2px;
    background-color: #75d1fe;

    &:nth-child(1) {
      top: -3px;
      left: -2px;
    }

    &:nth-child(2) {
      top: -3px;
      right: -2px;
    }

    &:nth-child(3) {
      bottom: -3px;
      left: -2px;
    }

    &:nth-child(4) {
      bottom: -3px;
      right: -2px;
    }
  }
}

.modal-body {
  padding: 0 0 40px 0;
}

.modal-content {
  margin-top: 30px;
  font-size: 24px;
  color: #ffffff;
  text-align: center;
}

.modal-header {
  @apply relative min-h-4 z-10;
}

.modal-title {
  text-align: center;
}

.modal-close {
  position: absolute;
  // top: 22px;
  right: 0px;
  font-family: 'SourceHanSansCN';
  font-size: 26px;
  color: #fff;
  cursor: pointer;
}

.modal-footer {
  margin: 20px 0 0 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 20px;
  min-width: 160px;
  height: 46px;
  font-family: 'AlibabaPuHuiTi';
  font-size: 18px;
  color: #ffffff;
  border: none;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-confirm {
  background-color: #006cc8;
  box-shadow: inset 0 0 15px 0px rgba(107, 225, 255, 1);
}

.btn-cancel {
  background-color: #055091;
  box-shadow: inset 0px 0px 8.3px 0px rgba(78, 210, 255, 0.5);
}

.btn:hover {
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.15);
}

.btn-confirm:hover {
  background-color: #0080e6;
  box-shadow:
    inset 0 0 20px 0px rgba(107, 225, 255, 1),
    0 5px 8px rgba(0, 0, 0, 0.15);
}

.btn-cancel:hover {
  background-color: #0665a8;
  box-shadow:
    inset 0px 0px 12px 0px rgba(78, 210, 255, 0.7),
    0 5px 8px rgba(0, 0, 0, 0.15);
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;

  .modal-container {
    transition:
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.3s ease;
  }
}

.modal-enter-from {
  opacity: 0;

  .modal-container {
    transform: translateY(20px);
    opacity: 0;
  }
}

.modal-leave-to {
  opacity: 0;

  .modal-container {
    transform: translateY(20px);
    opacity: 0;
  }
}
</style>
