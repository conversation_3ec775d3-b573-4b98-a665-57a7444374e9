<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-19
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-13
-->
<template>
  <div class="cesium-map" ref="cesiumMapRef" v-show="route.meta.title === '节目覆盖'">
    <vc-config-provider :access-token="token" v-if="loaded">
      <vc-viewer
        @ready="onViewerReady"
        :showCredit="false"
        :infoBox="false"
        :selectionIndicator="false"
      ></vc-viewer>
    </vc-config-provider>
    <Transition name="fade-mask">
      <div
        class="fixed left-0 top-0 w-full h-full bg-#001a33 flex items-center justify-center"
        v-if="!cesiumStore.sceneLoaded"
      >
        <div class="loading-container text-center">
          <div class="loading-spinner"></div>
          <div class="text-#00e6ff text-xl font-light mt-2">
            场景加载中<span class="dot">...</span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { VcConfigProvider, VcViewer } from 'vue-cesium'
import type { VcReadyObject } from 'vue-cesium/es/utils/types'
import { useCesiumStore } from '@/stores/cesiumStore'
import { useRoute } from 'vue-router'
import { nextTick, onMounted, ref, useTemplateRef, watch } from 'vue'
import { throttle } from 'lodash-es'

const route = useRoute()
const cesiumMapRef = useTemplateRef('cesiumMapRef')
const loaded = ref(false)

watch(
  () => route.meta.title,
  (newVal) => {
    if (newVal === '节目覆盖') {
      nextTick(() => {
        loaded.value = true
      })
    }
  },
)

const cesiumStore = useCesiumStore()

const token =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.y-idK4LloixOPvaYK91jcxwchb6VgxUtAge8JXksSZw'

const onViewerReady = (readyObj: VcReadyObject) => {
  window.viewer = readyObj.viewer
  const layer = readyObj.viewer.imageryLayers.addImageryProvider(
    new Cesium.SingleTileImageryProvider({
      url: 'imagery/night.jpg',
    }),
    100,
  )
  readyObj.viewer.scene.imageryLayers.add(layer)
  // 设置鼠标滚轮进行视图 zoom 变化
  readyObj.viewer.scene.screenSpaceCameraController.zoomEventTypes = [Cesium.CameraEventType.WHEEL]
  // 设置鼠标右键拖动地图, 允许用户在3D和2.5D模式下倾斜，或者在2D模式下旋转的输入
  readyObj.viewer.scene.screenSpaceCameraController.tiltEventTypes = [
    Cesium.CameraEventType.RIGHT_DRAG,
  ]
  readyObj.viewer.scene.screenSpaceCameraController.minimumZoomDistance = 0.8
  setTimeout(() => {
      cesiumStore.setSceneLoaded(true)
    }, 2000)
  // 暴露坐标转换工具
  nextTick(() => {
    updateTransformTool()
    exposeTransformTool()

    // 开始监听缩放变化
    const cleanup = watchScaleChanges()

    // 存储清理函数以便后续清理
    if (cleanup) {
      window.cesiumTransformToolCleanup = cleanup
    }
  })
}

// 新增：获取当前缩放比例
const getCurrentScale = () => {
  const screenWrapper = document.querySelector('.screen-wrapper')
  if (!screenWrapper) return 1

  try {
    const computedStyle = getComputedStyle(screenWrapper)
    const transform = computedStyle.transform
    if (transform === 'none') return 1

    const matrix = transform.match(/matrix\(([^)]+)\)/)
    if (matrix) {
      const values = matrix[1].split(',').map((v) => parseFloat(v.trim()))
      return values[0] // 第一个值是scaleX
    }

    const scale = transform.match(/scale\(([^)]+)\)/)
    if (scale) {
      return parseFloat(scale[1])
    }

    return 1
  } catch (error) {
    console.warn('获取缩放比例失败:', error)
    return 1
  }
}

// 新增：获取当前缩放比例
const getCurrentMargin = (): [number, number] => {
  const screenWrapper = document.querySelector('.screen-wrapper')
  if (!screenWrapper) return [0, 0]

  try {
    const computedStyle = getComputedStyle(screenWrapper)
    const margin = computedStyle.margin.split(' ').map((v) => parseFloat(v))
    return margin.length === 1 ? [margin[0], margin[0]] : (margin as [number, number])
  } catch (error) {
    console.warn('获取margin失败:', error)
    return [0, 0]
  }
}

// 新增：坐标转换工具函数
const transformMousePosition = (mousePosition: { x: number; y: number }) => {
  const scale = getCurrentScale()
  const margin = getCurrentMargin()

  if (scale === 1) return mousePosition

  // 获取Cesium容器的位置信息
  const cesiumContainer = cesiumMapRef.value
  if (!cesiumContainer) return mousePosition

  const rect = cesiumContainer.getBoundingClientRect()

  // 计算相对于Cesium容器的坐标
  const relativeX = mousePosition.x - rect.left + margin[1]
  const relativeY = mousePosition.y - rect.top + margin[0]

  // 应用缩放比例转换
  const transformedX = relativeX / scale
  const transformedY = relativeY / scale

  return {
    x: transformedX,
    y: transformedY,
  }
}

// 新增：将坐标转换工具暴露到全局，供其他组件使用
const exposeTransformTool = () => {
  if (window.cesiumTransformTool) return

  window.cesiumTransformTool = {
    getCurrentScale,
    getCurrentMargin,
    transformMousePosition,
    // 为Cesium事件提供坐标转换
    transformCesiumEvent: (movement: any) => {
      const originalPosition = movement.position || movement
      const transformedPosition = transformMousePosition(originalPosition)
      return {
        ...movement,
        position: transformedPosition,
      }
    },
  }
}

// 新增：监听缩放变化并更新坐标转换工具
const updateTransformTool = () => {
  if (window.cesiumTransformTool) {
    // 更新全局工具中的getCurrentScale函数引用
    window.cesiumTransformTool.getCurrentScale = getCurrentScale
    window.cesiumTransformTool.transformMousePosition = transformMousePosition
    window.cesiumTransformTool.transformCesiumEvent = (movement: any) => {
      const originalPosition = movement.position || movement
      const transformedPosition = transformMousePosition(originalPosition)
      return {
        ...movement,
        position: transformedPosition,
      }
    }
  }
}

// 重新设计：监听缩放变化而不是反向缩放
const watchScaleChanges = () => {
  const screenWrapper = document.querySelector('.screen-wrapper')
  if (!screenWrapper) return

  // 使用ResizeObserver监听元素大小变化
  const resizeObserver = new ResizeObserver(() => {
    updateTransformTool()
  })

  // 使用MutationObserver监听样式变化
  const mutationObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
        updateTransformTool()
      }
    })
  })

  resizeObserver.observe(screenWrapper)
  mutationObserver.observe(screenWrapper, {
    attributes: true,
    attributeFilter: ['style'],
  })

  // 清理函数
  return () => {
    resizeObserver.disconnect()
    mutationObserver.disconnect()
  }
}

onMounted(() => {
  // 监听窗口大小变化，更新坐标转换工具
  window.addEventListener('resize', () => {
    setTimeout(() => {
      updateTransformTool()
    }, 500)
  })
})
</script>

<style scoped lang="scss">
.cesium-map {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 230, 255, 0.2);
  border-radius: 50%;
  border-top-color: #00e6ff;
  box-shadow: 0 0 15px rgba(0, 230, 255, 0.3);
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.fade-mask-enter-active,
.fade-mask-leave-active {
  transition: opacity 0.3s ease;
}

.fade-mask-enter-from,
.fade-mask-leave-to {
  opacity: 0;
}

.dot {
  display: inline-block;
  height: 1em;
  line-height: 1;
  vertical-align: -0.25em;
  overflow: hidden;
}

.dot::before {
  display: block;
  content: '...\A..\A.';
  white-space: pre-wrap;
  animation: dot 3s infinite step-start both;
}

@keyframes dot {
  33% {
    transform: translateY(-2em);
  }

  66% {
    transform: translateY(-1em);
  }
}
</style>
