<template>
  <div class="title w-full h-15 bg-no-repeat bg-left-top bg-cover pl-15 pt-2">
    <p class="title-text">{{ title }}</p>
  </div>
</template>

<script setup lang="ts">
import type { TitleProps } from './types'

const props = defineProps<TitleProps>()
</script>

<style scoped lang="scss">
.title {
  background-image: url('@/assets/images/overview/title_bg.png');
}
.title-text {
  width: fit-content;
  font-family: 'JiangChengXieHei-700W', sans-serif;
  font-size: 29px;
  color: #fff;

  @include text-gradual(#31beff, #fff);
}
</style>
