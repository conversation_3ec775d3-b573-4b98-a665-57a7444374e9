{"common": {"scrollbarColor": "#00c0f3", "scrollbarColorHover": "#0c6dcf", "scrollbarWidth": "4px"}, "Tree": {"fontSize": "14px", "nodeHeight": "35px", "arrowColor": "#fff", "nodeTextColor": "rgba(255, 255, 255, 1)", "nodeColorActive": "linear-gradient(272deg, rgba(17,106,183,0) 0%, #116AB7 100%)", "nodeColorPressed": "#116AB7", "nodeColorHover": "linear-gradient(272deg, rgba(17,106,183,0) 0%, #116AB7 100%)", "nodeTextColorDisabled": "transparent"}, "Checkbox": {"color": "transparent", "checkMarkColor": "#29F1D2FF", "colorChecked": "rgba(255,255,255,0.2)", "borderColor": "rgba(255,255,255,0.5)", "borderChecked": "1px solid #9ED2FF80", "border": "1px solid #9ED2FF80", "borderRadius": "3px", "borderFocus": "1px solid rgba(255,255,255,1)", "textColor": "#D0DEEEFF"}, "Select": {"peers": {"InternalSelection": {"color": "rgba(24, 45, 98, 0.70)", "colorActive": "rgba(24, 45, 98, 1)", "textColor": "#fff", "arrowColor": "#fff", "heightMedium": "30px", "border": "1px solid rgba(158, 210, 255, 0.50)", "borderHover": "1px solid rgba(0, 119, 255, 1)", "boxShadowActive": "0 0 0 2px rgba(0, 119, 255, 0.2)", "boxShadowFocus": "0 0 0 2px rgba(0, 119, 255, 0.2)", "fontSizeTiny": "13px"}, "InternalSelectMenu": {"color": "rgba(24, 45, 98, 0.70)", "optionColorActive": "rgba(23, 192, 255, 1)", "optionTextColor": "#fff", "optionTextColorActive": "#fff", "optionTextColorPressed": "rgba(23, 192, 255, 0.8)", "optionColorPending": "rgba(23, 192, 255, 0.5)", "optionColorActivePending": "rgba(23, 192, 255, 0.5)"}}}, "Menu": {"itemColorHover": "rgba(0, 124, 255, 0.6)", "itemColorActive": "rgba(0, 124, 255, 1)", "itemTextColor": "#eee", "itemTextColorHover": "#fff", "itemTextColorActive": "#fff", "itemTextColorDisabled": "#ccc", "itemIconColor": "#eee", "itemIconColorHover": "#fff", "itemIconColorActive": "#fff", "itemIconColorDisabled": "#ccc", "itemIconColorCollapsed": "#fff"}, "Input": {"color": "rgba(24, 45, 98, 0.70)", "colorFocus": "rgba(24, 45, 98, 1)", "textColor": "#fff", "arrowColor": "#fff", "heightMedium": "30px", "border": "1px solid rgba(158, 210, 255, 0.50)", "borderHover": "1px solid rgba(0, 119, 255, 1)", "boxShadowActive": "0 0 0 2px rgba(0, 119, 255, 0.2)", "boxShadowFocus": "0 0 0 2px rgba(0, 119, 255, 0.2)"}, "Radio": {"textColor": "#D0DEEE", "color": "rgba(24, 45, 98, 0.70)", "colorActive": "rgba(24, 45, 98, 1)", "borderColor": "rgba(158, 210, 255, 0.50)", "borderColorActive": "rgba(0, 119, 255, 1)", "dotColorActive": "#29F1D2", "boxShadow": "inset 0 0 0 1px #9ED2FF", "boxShadowActive": "inset 0 0 0 1px #9ED2FF", "boxShadowFocus": "inset 0 0 0 1px #9ED2FF", "boxShadowHover": "inset 0 0 0 1px #9ED2FF"}, "DatePicker": {"panelColor": "rgba(24, 45, 98, 0.70)", "panelTextColor": "rgba(255, 255, 255, 1)", "calendarTitleTextColor": "rgba(255, 255, 255, 1)", "calendarTitleColorHover": "rgba(0, 189, 255, 0.8)", "itemTextColor": "rgba(234, 234, 234, 0.8)", "itemColorHover": "rgba(24, 45, 98, 1)", "itemColorActive": "rgba(14, 95, 255, 1)", "itemColorIncluded": "rgba(14, 95, 255, 0.3)", "itemTextColorCurrent": "rgba(14, 95, 255, 1)", "panelActionDividerColor": "rgba(4, 88, 207, 1)", "calendarDividerColor": "rgba(4, 88, 207, 1)", "calendarDaysDividerColor": "rgba(4, 88, 207, 1)"}, "Button": {"colorPrimary": "#0e5fff", "colorHoverPrimary": "#508BFFFF", "colorPressedPrimary": "#0e5fff", "colorFocusPrimary": "#508BFFFF", "colorDisabledPrimary": "#2B61CCFF", "borderPrimary": "1px solid #00000000"}, "DataTable": {"borderColor": "rgba(208, 222, 238, 0.3)", "tdColorHover": "rgba(9, 61, 126, 0.5)", "thColor": "rgba(9, 61, 126, 0.5)", "thTextColor": "rgba(208, 222, 238, 1)", "tdColor": "rgba(3, 43, 93, 0.8)", "tdTextColor": "rgba(208, 222, 238, 1)"}}