/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-13
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-18
 */
import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/layout/layout.vue'
import {
  initScene as initOverviewScene,
  clearScene as clearOverviewScene,
} from '@/views/overview/index.ts'
import {
  initScene as initProgramCoverageScene,
  clearScene as clearProgramCoverageScene,
} from '@/views/program-coverage/index'
import {
  initScene as initDataCenterLayoutScene,
  clearScene as clearDataCenterLayoutScene,
} from '@/views/data-center-layout/index.ts'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/overview',
    children: [
      {
        path: 'overview',
        name: 'AreaOverview',
        component: () => import('@/views/overview/overview.vue'),
        meta: {
          title: '园区全景',
          threeScene: true,
          initScene: initOverviewScene,
          clearScene: clearOverviewScene,
        },
      },
      {
        path: 'program-coverage',
        name: 'ProgramCoverage',
        component: () => import('@/views/program-coverage/programCoverage.vue'),
        meta: {
          title: '节目覆盖',
          threeScene: false,
        },
      },
      {
        path: 'data-center-layout',
        name: 'DataCenterLayout',
        component: () => import('@/views/data-center-layout/dataCenterLayout.vue'),
        meta: {
          title: '机房布局',
          threeScene: true,
          initScene: initDataCenterLayoutScene,
          clearScene: clearDataCenterLayoutScene,
        },
      },
      {
        path: 'system-topology',
        name: 'SystemTopology',
        component: () => import('@/views/system-topology/systemTopology.vue'),
        meta: {
          threeScene: false,
          title: '系统拓补',
        },
      },
    ],
  },
]

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
})

router.beforeEach(async (to, from, next) => {
  if (typeof from.meta.clearScene === 'function') {
    await from.meta.clearScene()
  }
  if (typeof to.meta.initScene === 'function') {
    await to.meta.initScene()
  }
  next()
})

export default router
