.page-left-enter-active {
  animation: page-left-enter 0.4s ease-in-out;
}
.page-left-leave-active {
  animation: page-left-leave 0.2s ease-in-out;
}

@keyframes page-left-enter {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0%);
    opacity: 1;
  }
}

@keyframes page-left-leave {
  0% {
    transform: translateX(0%);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.page-right-enter-active {
  animation: page-right-enter 0.4s ease-in-out;
}
.page-right-leave-active {
  animation: page-right-leave 0.2s ease-in-out;
}

@keyframes page-right-enter {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0%);
    opacity: 1;
  }
}

@keyframes page-right-leave {
  0% {
    transform: translateX(0%);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.fade-enter-active {
  animation: fade-in 1s ease-in-out;
}
.fade-leave-active {
  animation: fade-out 0.2s ease-in-out;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.scale-fade-enter-active,
.scale-fade-leave-active {
  transition: all 0.5s ease;
  transform-origin: left top;
}

.scale-fade-enter-from {
  opacity: 0;
  transform: scale(0) translate(-120px, 60px);
}

.scale-fade-enter-to {
  opacity: 1;
  transform: scale(1) translate(0%, 0%);
}

.scale-fade-leave-from {
  opacity: 1;
  transform: scale(1) translate(0%, 0%);
}

.scale-fade-leave-to {
  opacity: 0;
  transform: scale(0) translate(-120px, -60px);
}

.fade-mask-enter-active,
.fade-mask-leave-active {
  transition: opacity 0.5s ease;
}

.fade-mask-enter-from,
.fade-mask-leave-to {
  opacity: 0;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease-out;
}

.slide-fade-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}
