@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei-2.ttf') format('truetype');
}

@font-face {
  font-family: 'JiangChengXieHei-700W';
  src: url('@/assets/fonts/JiangChengXieHei-700W.ttf') format('truetype');
}

@font-face {
  font-family: 'DINNextLTPro-Medium';
  src: url('@/assets/fonts/DINNextLTPro-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'AlibabaPuHuiTi';
  src: url('@/assets/fonts/AlibabaPuHuiTi.ttf') format('truetype');
}

@font-face {
  font-family: 'PangMenZhengDao';
  src: url('@/assets/fonts/PangMenZhengDao.ttf') format('truetype');
}

@font-face {
  font-family: 'ShiS<PERSON><PERSON>hongHeiJianTi';
  src: url('@/assets/fonts/ShiShangZhongHeiJianTi.ttf') format('truetype');
}

@font-face {
  font-family: 'DINNextLTPro';
  src: url('@/assets/fonts/DINNextLTPro.ttf') format('truetype');
}

@font-face {
  font-family: 'DS-Digital-Bold';
  src: url('@/assets/fonts/DS-Digital-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'SourceHanSansCN';
  src: url('@/assets/fonts/SourceHanSansCN.otf') format('opentype');
}
