// 文字超出x行自动显示省略号
@mixin text-overflow($line: 2) {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
}

// 渐变文字
@mixin text-gradual($color1: rgb(255, 255, 255), $color2: rgb(146, 232, 250)) {
  background: linear-gradient(180deg, $color1 0%, $color2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
