/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-20
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-12
 */

import type { MessageApiInjection } from 'naive-ui'
declare global {
  interface Window {
    viewer: Cesium.Viewer
    cesiumTransformTool?: {
      getCurrentScale: () => number
      getCurrentMargin: () => [number, number]
      transformMousePosition: (mousePosition: { x: number; y: number }) => { x: number; y: number }
      transformCesiumEvent: (movement: any) => any
    }
    cesiumTransformToolCleanup?: () => void
  }
}

export {}
