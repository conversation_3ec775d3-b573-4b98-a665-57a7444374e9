<template>
  <div class="space-y-7.5">
    <Container title="园区数据">
      <div class="h-[210px] flex items-center gap-x-17 px-6 pt-5">
        <div v-for="(item, index) in parkData" :key="index" class="flex flex-col items-center">
          <img class="size-27.5" :src="item.icon" :alt="item.name" />
          <p class="text-[30px] font-500 text-[#32DCFB] font-['DINNextLTPro-Medium']">
            {{ item.area }}
          </p>
          <p class="name font-['AlibabaPuHuiTi'] font-italic text-base">
            {{ item.name }}
          </p>
        </div>
      </div>
    </Container>

    <Container title="值班信息">
      <div class="h-[350px] overflow-hidden pt-3 pr-2">
        <!-- 白班 -->
        <div class="h-40">
          <div class="line"></div>
          <div class="flex items-center">
            <img class="size-35" src="@/assets/images/overview/白天.png" alt="白班  " />

            <div class="w-100">
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10">值班长:</div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#99F17A]">已签到</div>
              </div>
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10">值班员:</div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#99F17A]">已签到</div>
              </div>
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10"></div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#FFD631]">未签到</div>
              </div>
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10"></div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#FFD631]">未签到</div>
              </div>
            </div>
          </div>
        </div>

        <div class="line"></div>

        <!-- 夜班 -->
        <div class="h-40">
          <div class="flex items-center">
            <img class="size-35" src="@/assets/images/overview/黑夜.png" alt="晚班" />
            <div class="w-100">
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10">值班长:</div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#99F17A]">已签到</div>
              </div>
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10">值班员:</div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#99F17A]">已签到</div>
              </div>
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10"></div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#FFD631]">未签到</div>
              </div>
              <div class="tr grid grid-cols-3 h-10 text-lg font-['AlibabaPuHuiTi']">
                <div class="leading-10"></div>
                <div class="leading-10">王小明</div>
                <div class="leading-10 text-[#FFD631]">未签到</div>
              </div>
            </div>
          </div>
        </div>

        <div class="line"></div>
      </div>
    </Container>

    <Container title="人员信息">
      <div>
        <div class="tab-container">
          <div class="dot left-[-2px] top-[-2px]"></div>
          <div class="dot right-[-2px] top-[-2px]"></div>
          <div class="dot left-[-2px] bottom-[-2px]"></div>
          <div class="dot right-[-2px] bottom-[-2px]"></div>
          <div
            v-for="(tab, index) in tabsData"
            :key="index"
            class="tab-item w-30 h-10 cursor-pointer"
            :class="{ active: currentTabIndex === index }"
            @click="currentTabIndex = index"
          >
            {{ tab }}
          </div>
        </div>
        <!-- 功勋荣誉 -->
        <n-carousel v-if="currentTabIndex === 0" :show-dots="false">
          <n-carousel-item class="relative" v-for="(item, index) in 4" :key="index">
            <img
              class="h-[364px] w-full object-cover"
              :src="`https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel${item}.jpeg`"
            />
            <div class="absolute bottom-0 left-0 w-full h-15 bg-[#202738]/80 flex">
              <div
                class="size-15 flex items-center justify-center leading-6 font-['PangMenZhengDao']"
                style="
                  background: linear-gradient(45deg, rgba(50, 220, 251, 0.39) 0%);
                  border: 1px solid;
                  opacity: 0.8;
                  border-image: linear-gradient(45deg, #32dcfb, #ffffff) 10 10;
                "
              >
                科技<br />之星
              </div>
              <div
                class="text-white text-[23px] leading-[38px] flex-1 flex items-center justify-center"
              >
                播出机房：陈喆光
              </div>
            </div>
          </n-carousel-item>
        </n-carousel>

        <!-- 学历结构 -->
        <div v-else class="h-[364px]">
          <Echart :options="options" />
        </div>
      </div>
    </Container>
  </div>
  <environment />
  <UpsLabel />
  <antennaPanel v-if="threeStore.sceneLoaded" />
  <AntennaLabel />
  <BuildingLabel />
  <Teleport to=".screen-wrapper">
    <Icon
      name="fuwei"
      class="fuwei"
      :style="{ left: layoutStore.showLeftPanel ? '640px' : '50px' }"
      title="复位"
      @click="locate"
    />
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue'
import { getDutyInfoRmationApi, getHonorsApi, getStationInfoApi } from '@/api/station'
import { getAssetsImage } from '@/utils/common'
import { NCarousel, NCarouselItem } from 'naive-ui'
import Container from '@/components/Container/Container.vue'
import Echart from '@/components/Echart/Echart.vue'
import type { EChartsOption } from 'echarts'
import UpsLabel from './components/upsLabel.vue'
import environment from './components/environment.vue'
import antennaPanel from './components/antennaPanel.vue'
import AntennaLabel from './components/antennaLabel.vue'
import BuildingLabel from './components/buildingLabel.vue'
import { locate } from './index'
import Icon from '@/components/Icon/Icon.vue'
import { useLayoutStore } from '@/stores/layoutStore'
import { useThreeStore } from '@/stores/threeStore'

const layoutStore = useLayoutStore()
const threeStore = useThreeStore()

// 园区数据
const parkData = ref([
  {
    key: 'total',
    name: '总占地面积',
    area: '0㎡',
    icon: getAssetsImage('总占地面积.png', 'overview'),
  },
  {
    key: 'building',
    name: '建筑面积',
    area: '0㎡',
    icon: getAssetsImage('建筑面积.png', 'overview'),
  },
  {
    key: 'green',
    name: '绿化面积',
    area: '0㎡',
    icon: getAssetsImage('绿化面积.png', 'overview'),
  },
])

const currentTabIndex = ref(1)
const tabsData = ['功勋荣誉', '学历结构']
// 学历结构 echarts options
const options: EChartsOption = {
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'horizontal',
    left: 'center',
    top: '10',
    icon: 'roundRect',
    itemGap: 24,
    itemWidth: 14,
    itemHeight: 6,
    textStyle: {
      fontFamily: 'AlibabaPuHuiTi',
      fontSize: 16,
      color: '#FCFCFC',
      opacity: 0.8,
    },
    data: [
      { name: '小学', itemStyle: { color: '#FFC523' } },
      { name: '初中', itemStyle: { color: '#59DBD1' } },
      { name: '高中', itemStyle: { color: '#FFA29D' } },
      { name: '专科', itemStyle: { color: '#00C6FF' } },
      { name: '本科', itemStyle: { color: '#2A63D8' } },
      { name: '研究生', itemStyle: { color: '#FF9338' } },
    ],
  },
  series: [
    // 内圆虚线装饰
    {
      name: '内圆装饰',
      type: 'pie',
      radius: ['44.5%', '49%'],
      center: ['50%', '52%'],
      silent: true,
      data: Array.from({ length: 90 }, (_, i) => ({
        value: i % 2 === 0 ? 3 : 6,
        itemStyle: {
          color: i % 2 === 0 ? '#339A91' : 'transparent',
          borderWidth: 0,
          borderRadius: 2,
        },
        label: { show: false },
        labelLine: { show: false },
      })),
    },
    // 主环形图
    {
      name: '学历结构',
      type: 'pie',
      radius: ['52%', '60%'],
      center: ['50%', '52%'],
      padAngle: 4,
      avoidLabelOverlap: false,
      data: [
        {
          value: 200,
          name: '小学',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#05172A' },
                { offset: 1, color: '#FFC523' },
              ],
            },
          },
          labelLine: {
            lineStyle: {
              color: '#FFC523',
            },
          },
        },
        {
          value: 735,
          name: '初中',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#05172A' },
                { offset: 1, color: '#59DBD1' },
              ],
            },
          },
          labelLine: {
            lineStyle: {
              color: '#59DBD1',
            },
          },
        },
        {
          value: 900,
          name: '高中',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#FFA29D' },
                { offset: 1, color: '#05172A' },
              ],
            },
          },
          labelLine: {
            lineStyle: {
              color: '#FFA29D',
            },
          },
        },
        {
          value: 400,
          name: '专科',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: '#00C6FF' },
                { offset: 1, color: '#05172A' },
              ],
            },
          },
          labelLine: {
            lineStyle: {
              color: '#00C6FF',
            },
          },
        },
        {
          value: 1150,
          name: '本科',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#05172A' },
                { offset: 1, color: '#2A63D8' },
              ],
            },
          },
          labelLine: {
            lineStyle: {
              color: '#2A63D8',
            },
          },
        },
        {
          value: 700,
          name: '研究生',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#FF9338' },
                { offset: 1, color: '#05172A' },
              ],
            },
          },
          labelLine: {
            lineStyle: {
              color: '#FF9338',
            },
          },
        },
      ],
      label: {
        show: true,
        position: 'outside',
        formatter: function (params: any) {
          return `{percent${params.dataIndex}|${params.percent}%}\n{name|${params.name}}`
        },
        rich: {
          percent0: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#FFC523',
            fontFamily: 'AlibabaPuHuiTi',
          },
          percent1: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#59DBD1',
            fontFamily: 'AlibabaPuHuiTi',
          },
          percent2: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#FFA29D',
            fontFamily: 'AlibabaPuHuiTi',
          },
          percent3: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#00C6FF',
            fontFamily: 'AlibabaPuHuiTi',
          },
          percent4: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#2A63D8',
            fontFamily: 'AlibabaPuHuiTi',
          },
          percent5: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#FF9338',
            fontFamily: 'AlibabaPuHuiTi',
          },
          name: {
            fontSize: 14,
            color: '#FFFFFF',
            fontFamily: 'AlibabaPuHuiTi',
            padding: [4, 0, 0, 0],
          },
        },
      },
      labelLine: {
        show: true,
        length: 20,
        length2: 10,
        lineStyle: {
          width: 1,
        },
      },
      emphasis: {
        scale: true,
        scaleSize: 5,
      },
    },
    // 中心文字
    {
      name: '中心文字',
      type: 'pie',
      radius: ['0%', '0%'],
      center: ['50%', '52%'],
      data: [{ value: 1, name: '' }],
      label: {
        show: true,
        position: 'center',
        formatter: '学历结构',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#FFFFFF',
        fontFamily: 'AlibabaPuHuiTi',
      },
      labelLine: { show: false },
      itemStyle: { color: 'transparent' },
      silent: true,
    },
  ],
}

// 获取园区数据
const fetchStationInfoData = async () => {
  const res = await getStationInfoApi()
  parkData.value.forEach((item) => {
    item.area = res[item.key] + '㎡'
  })
}
// 值班信息
const fetchDutyInfoData = async () => {
  const res = await getDutyInfoRmationApi()
  console.log('🚀 ~ fetchDutyInfoApi ~ res:', res)
}
// 功勋荣誉
const fetchHonorsData = async () => {
  const res = await getHonorsApi()
  console.log('🚀 ~ fetchHonorsApi ~ res:', res)
}

onBeforeMount(() => {
  fetchStationInfoData()
  fetchDutyInfoData()
  fetchHonorsData()
})
</script>

<style scoped lang="scss">
.name {
  color: #d1d6df;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.line {
  flex-shrink: 0;
  margin-left: 24px;
  width: 506px;
  height: 1px !important;
  background-color: rgba(255, 255, 255, 0.74);
}

.tr {
  padding-left: 20px;
  border-bottom: 1px solid rgba(216, 227, 238, 0.1);

  &:last-child {
    border-bottom: none;
  }

  div:first-child {
    padding-left: 24px;
  }

  &:nth-child(odd) {
    background: linear-gradient(90deg, rgba(27, 130, 183, 0.22) 0%, rgba(35, 104, 162, 0) 100%);
  }

  &:nth-child(even) {
    background: linear-gradient(90deg, rgba(27, 130, 183, 0.12) 0%, rgba(35, 104, 162, 0) 100%);
  }
}

.tab-container {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 6px;
  margin: 0 0 4px 0;
  column-gap: 5px;
  width: 100%;
  height: 50px;
  border: 1px solid;
  opacity: 0.5;
  border-image: linear-gradient(0deg, #81c5c8, #81c5c8) 10 10;
}

.tab-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #fff;
  background-image: linear-gradient(0deg, rgba(42, 215, 255, 0.2) 0%, rgba(42, 215, 255, 0) 100%);
  border: 1px solid;
  border-image: linear-gradient(0deg, #81c5c8, #81c5c8) 10 10;
  cursor: pointer;

  &.active {
    background-image: url('@/assets/images/overview/tab_bg.png') !important;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    border: none !important;
    /* &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(0deg, rgba(94, 86, 55, 0.8) 0%, rgba(94, 86, 55, 0.2) 100%);
    } */
  }
}

.dot {
  @apply absolute size-[5px] rounded-full bg-white;
}

.fuwei {
  @apply fixed left-630px top-240px flex justify-center items-center rounded-full text-36px text-white bg-#1ea2ff88 w-54px h-54px z-10 cursor-pointer transition-all duration-300;

  &:hover {
    @apply bg-#1ea2ff;
  }
}
</style>
