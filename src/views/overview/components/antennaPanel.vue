<template></template>

<script lang="ts" setup>
import { viewer } from '@/three/viewer';
import * as THREE from 'three';
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import { until } from '@/utils';
import { onMounted, onUnmounted, ref } from 'vue';

const antennas = ref([
  {
    id: 0,
    name: "GDYQ-JSQ-ZT-0002_1",
    azimuth: 0,
    pitch: 0,
    polarizing: 0,
    power: 50,
    position: [
      -9.52351274287353,
      18.004013911112915,
      -15.117668200364813] // 左侧标签
  },
  {
    id: 1,
    name: "GDYQ-JSQ-ZT-0001_1",
    azimuth: 0,
    pitch: 0,
    polarizing: 0,
    power: 50,
    position: [
      -1.8781434286229046,
      13.410607039098118,
      0.4879819653900302] // 右侧标签，与左侧标签保持6个单位距离
  },
])

// 存储创建的CSS2D标签引用
const antennaLabels = ref<Map<number, CSS2DObject>>(new Map());

// 创建CSS2D渲染器
let css2dRenderer: CSS2DRenderer;

// 创建天线信息面板的HTML元素
const createAntennaPanelHTML = (antenna: any) => {
  const container = document.createElement('div');
  container.className = 'antenna-panel';

  // 设置样式 - 科幻风格
  container.style.cssText = `
    width: 200px;
    height: max-content;
    background: linear-gradient(135deg,
      rgba(51, 125, 255, 0.15) 0%,
      rgba(51, 125, 255, 0.08) 50%,
      rgba(51, 125, 255, 0.15) 100%);
    border: 1px solid rgba(51, 125, 255, 0.6);
    border-radius: 8px;
    box-shadow:
      0 0 20px rgba(51, 125, 255, 0.3),
      0 0 40px rgba(51, 125, 255, 0.1),
      inset 0 0 20px rgba(51, 125, 255, 0.1);
    backdrop-filter: blur(15px);
    font-family: "Inter", "Source Han Sans SC", "PingFang SC", "Microsoft YaHei", sans-serif;
    color: rgba(255, 255, 255, 0.9);
    overflow: hidden;
    position: relative;
    // transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    cursor: pointer;
    user-select: none;
    transform-style: preserve-3d;
    backface-visibility: hidden;
    z-index: 20;
  `;

  // 创建动态扫描线效果
  const scanLine = document.createElement('div');
  scanLine.style.cssText = `
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(0, 255, 255, 0.4) 15%,
      rgba(51, 125, 255, 0.8) 35%,
      rgba(138, 43, 226, 1) 50%,
      rgba(51, 125, 255, 0.8) 65%,
      rgba(0, 255, 255, 0.4) 85%,
      transparent 100%);
    box-shadow:
      0 0 10px rgba(0, 255, 255, 0.8),
      0 0 20px rgba(51, 125, 255, 0.6),
      0 0 30px rgba(138, 43, 226, 0.4);
    animation: scan 2s linear infinite;
    z-index: 2;
  `;
  container.appendChild(scanLine);

  // 创建科技感边框
  const techBorder = document.createElement('div');
  techBorder.style.cssText = `
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border: 1px solid transparent;
    border-radius: 8px;
    background: linear-gradient(45deg,
      rgba(51, 125, 255, 0.8),
      rgba(51, 125, 255, 0.4),
      rgba(51, 125, 255, 0.8));
    background-size: 200% 200%;
    animation: borderGlow 4s ease-in-out infinite;
    z-index: 1;
  `;
  container.appendChild(techBorder);

  // 创建发光边框效果
  const glowBorder = document.createElement('div');
  glowBorder.style.cssText = `
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
      rgba(51, 125, 255, 0.4),
      rgba(51, 125, 255, 0.2),
      rgba(51, 125, 255, 0.4));
    border-radius: 10px;
    z-index: -1;
    filter: blur(6px);
    opacity: 0.6;
    animation: pulse 2s ease-in-out infinite;
  `;
  container.appendChild(glowBorder);

  // 创建标题栏 - 科幻风格
  const titleBar = document.createElement('div');
  titleBar.style.cssText = `
    background: linear-gradient(90deg,
      rgba(51, 125, 255, 0.3) 0%,
      rgba(51, 125, 255, 0.1) 100%);
    padding: 8px 12px;
    border-bottom: 1px solid rgba(51, 125, 255, 0.4);
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-align: center;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 3;
  `;

  // 添加标题前的图标
  const titleIcon = document.createElement('span');
  titleIcon.innerHTML = '◈';
  titleIcon.style.cssText = `
    margin-right: 6px;
    color: rgba(51, 125, 255, 0.8);
    animation: rotate 4s linear infinite;
  `;
  titleBar.appendChild(titleIcon);

  const titleText = document.createElement('span');
  titleText.textContent = antenna.name;
  titleText.className = 'title-text'; // 添加类名以便更新
  titleBar.appendChild(titleText);

  container.appendChild(titleBar);

  // 创建内容区域 - 科幻风格
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    z-index: 3;
  `;

  // 创建信息行 - 科幻风格
  const createInfoRow = (label: string, value: string, className: string) => {
    const row = document.createElement('div');
    row.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      border-bottom: 1px solid rgba(51, 125, 255, 0.1);
      transition: all 0.2s ease;
    `;

    // 添加标签前的点
    const labelDot = document.createElement('span');
    labelDot.style.cssText = `
      color: rgba(51, 125, 255, 0.8);
      margin-right: 6px;
      font-size: 8px;
      animation: blink 2s ease-in-out infinite;
    `;
    labelDot.textContent = '●';
    row.appendChild(labelDot);

    const labelSpan = document.createElement('span');
    labelSpan.style.cssText = `
      color: rgba(255, 255, 255, 0.7);
      font-size: 11px;
      flex: 1;
    `;
    labelSpan.textContent = label;
    row.appendChild(labelSpan);

    const valueSpan = document.createElement('span');
    valueSpan.className = className; // 添加类名以便更新
    valueSpan.style.cssText = `
      color: rgba(255, 255, 255, 0.9);
      font-size: 12px;
      font-weight: 500;
      text-shadow: 0 0 8px rgba(51, 125, 255, 0.6);
    `;
    if (className === 'power-text') {
      let color = 'rgba(0,255,0,0.9)'
      if (antenna.power > 50) {
        color = 'rgba(255,0,0,0.9)';
      } else if (antenna.power > 20) {
        color = 'rgba(255,255,0,0.9)';
      }
      valueSpan.style.cssText = `
      color: ${color};
      font-size: 12px;
      font-weight: 500;
      text-shadow: 0 0 8px rgba(51, 125, 255, 0.6);
    `;
    }
    valueSpan.textContent = value;
    row.appendChild(valueSpan);

    // 添加悬停效果
    row.addEventListener('mouseenter', () => {
      row.style.background = 'rgba(51, 125, 255, 0.1)';
      row.style.borderBottom = '1px solid rgba(51, 125, 255, 0.3)';
    });

    row.addEventListener('mouseleave', () => {
      row.style.background = 'transparent';
      row.style.borderBottom = '1px solid rgba(51, 125, 255, 0.1)';
    });

    return row;
  };

  // 添加各项信息
  content.appendChild(createInfoRow('方位角', `${antenna.azimuth}°`, 'azimuth-text'));
  content.appendChild(createInfoRow('俯仰角', `${antenna.pitch}°`, 'pitch-text'));
  content.appendChild(createInfoRow('极化角', `${antenna.polarizing}°`, 'polarizing-text'));
  content.appendChild(createInfoRow('功率', `${antenna.power} W`, 'power-text'));

  container.appendChild(content);

  // 创建底部状态条
  const statusBar = document.createElement('div');
  statusBar.style.cssText = `
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
      rgba(51, 125, 255, 0.8) 0%,
      rgba(51, 125, 255, 0.4) 50%,
      rgba(51, 125, 255, 0.8) 100%);
    background-size: 200% 100%;
    animation: flow 3s linear infinite;
    z-index: 3;
  `;
  container.appendChild(statusBar);

  // 添加点击事件
  container.addEventListener('click', () => {
    // 这里可以添加点击后的交互逻辑
  });

  return container;
};

// 更新天线标签内容
const updateAntennaLabel = (antenna: any) => {
  const labelObject = antennaLabels.value.get(antenna.id);
  if (labelObject && labelObject.element) {
    // 获取现有的HTML元素
    const existingElement = labelObject.element;

    // 只更新内容，不删除重建
    // 更新标题
    const titleText = existingElement.querySelector('.title-text');
    if (titleText) {
      titleText.textContent = antenna.name;
    }

    // 更新方位角
    const azimuthText = existingElement.querySelector('.azimuth-text');
    if (azimuthText) {
      azimuthText.textContent = `${antenna.azimuth}°`;
    }

    // 更新俯仰角
    const pitchText = existingElement.querySelector('.pitch-text');
    if (pitchText) {
      pitchText.textContent = `${antenna.pitch}°`;
    }

    // 更新极化角
    const polarizingText = existingElement.querySelector('.polarizing-text');
    if (polarizingText) {
      polarizingText.textContent = `${antenna.polarizing}°`;
    }

    // 更新功率
    const powerText = existingElement.querySelector('.power-text');
    if (powerText) {
      powerText.textContent = `${antenna.power} W`;
      if (antenna.power > 50) {
        powerText.style.color = 'rgba(255,0,0,0.9)';
      } else if (antenna.power > 20) {
        powerText.style.color = 'rgba(255,255,0,0.9)';
      } else {
        powerText.style.color = 'rgba(0,255,0,0.9)'
      }
    }
  }
};

// 初始化CSS2D渲染器
const initCSS2DRenderer = () => {
  css2dRenderer = new CSS2DRenderer();
  css2dRenderer.setSize(2560, 1440);
  css2dRenderer.domElement.style.position = 'fixed';
  css2dRenderer.domElement.style.top = '0';
  css2dRenderer.domElement.style.left = '0';
  css2dRenderer.domElement.style.pointerEvents = 'none';
  css2dRenderer.domElement.style.zIndex = '100'; // 确保在最上层

  // 将CSS2D渲染器添加到页面
  document.querySelector('.layout')?.appendChild(css2dRenderer.domElement);
};

// 创建CSS2D场景
const css2dScene = new THREE.Scene();

const init3dPanel = () => {
  until(() => viewer?.isInited).then(() => {
    // 初始化CSS2D渲染器
    initCSS2DRenderer();

    // 为每个antenna在场景中指定位置创建CSS2D标签
    antennas.value.forEach(antenna => {
      try {
        // 创建HTML元素
        const htmlElement = createAntennaPanelHTML(antenna);

        // 创建CSS2D对象
        const labelObject = new CSS2DObject(htmlElement);

        // 设置标签位置 - 直接使用数据中的实际坐标
        labelObject.position.set(antenna.position[0], antenna.position[1], antenna.position[2]);

        // 设置固定的缩放值
        labelObject.scale.setScalar(0.025);

        // 存储天线信息到标签中
        labelObject.userData.antennaName = antenna.name;
        labelObject.userData.antennaId = antenna.id;

        // 添加到CSS2D场景
        css2dScene.add(labelObject);

        // 存储标签引用
        antennaLabels.value.set(antenna.id, labelObject);

      } catch (error) {
        console.error(`创建CSS2D天线标签失败: ${antenna.name}`, error);
      }
    });

    // 添加渲染循环，让CSS2D标签保持固定
    const updateCSS2DLabels = () => {
      // 检查每个标签的可见性
      antennaLabels.value.forEach((labelObject) => {
        // 计算标签到相机的距离
        const distance = labelObject.position.distanceTo(viewer.camera.position);

        // 当距离大于20时隐藏标签，小于等于20时显示标签
        labelObject.visible = distance <= 40
      });

      // 渲染CSS2D场景
      css2dRenderer.render(css2dScene, viewer.camera);
    };

    // 将更新函数添加到渲染循环
    viewer.addRenderCallback(updateCSS2DLabels);
  });
};

// 更新所有天线标签
const updateAllAntennaLabels = () => {
  antennas.value.forEach(antenna => {
    updateAntennaLabel(antenna);
  });
};

// 模拟数据更新（用于演示）
const simulateDataUpdate = () => {
  setInterval(() => {
    antennas.value.forEach(antenna => {
      // 随机更新一些数据用于演示
      antenna.azimuth = Math.floor(Math.random() * 360);
      antenna.pitch = Math.floor(Math.random() * 90);
      antenna.polarizing = Math.floor(Math.random() * 180);
      antenna.power = Math.floor(Math.random() * 100) + 10;
    });

    // 更新标签内容
    updateAllAntennaLabels();
  }, 5000); // 每5秒更新一次
};

// 处理窗口大小变化
const handleResize = () => {
  if (css2dRenderer) {
    css2dRenderer.setSize(window.innerWidth, window.innerHeight);
  }
};

onMounted(() => {
  init3dPanel();
  // 启动数据更新模拟（可选）
  simulateDataUpdate();

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 清除所有antenna的CSS2D标签
  try {
    // 移除所有天线标签
    antennaLabels.value.forEach((labelObject, id) => {
      // 从CSS2D场景中移除
      css2dScene.remove(labelObject);

      // 清理HTML元素
      if (labelObject.element && labelObject.element.parentNode) {
        labelObject.element.parentNode.removeChild(labelObject.element);
      }
    });

    // 清空引用
    antennaLabels.value.clear();

    // 清理CSS2D渲染器
    if (css2dRenderer && css2dRenderer.domElement.parentNode) {
      css2dRenderer.domElement.parentNode.removeChild(css2dRenderer.domElement);
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize);

  } catch (error) {
    console.error('清理CSS2D天线标签时出错:', error);
  }
});
</script>

<style lang="scss">
/* 全局样式，确保CSS2D标签正确显示 */
:global(body) {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

:global(.antenna-panel) {
  /* 确保标签在3D场景中正确显示 */
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* 科幻风格动画定义 */
:global {
  @keyframes scan {
    0% {
      left: -100%;
    }

    100% {
      left: 100%;
    }
  }

  @keyframes borderGlow {

    0%,
    100% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 0.6;
      transform: scale(1);
    }

    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes blink {

    0%,
    50% {
      opacity: 1;
    }

    51%,
    100% {
      opacity: 0.3;
    }
  }

  @keyframes flow {
    0% {
      background-position: 0% 0%;
    }

    100% {
      background-position: 200% 0%;
    }
  }
}
</style>
