<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-05
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-11
-->
<template>
  <dataModal :title="title" v-model:visible="visible"
    :style="{ top: `${clickPosition?.y}px`, left: `${clickPosition?.x}px` }" @close="handleClose">
    <div class="detail">
      <div class="detail-item" v-for="item in detail" :key="item.key">
        <div class="detail-item-name">{{ item.name }}:</div>
        <div class="detail-item-value">{{ item.value }}</div>
      </div>
    </div>
    <uiTable :columns="alarmColumns" :data="alarmList" v-if="alarmList.length">
      <template #level="{ scope }">
        <img :src="alarmLevelIconMap.get(scope.level)" :alt="scope.level" />
      </template>
      <template #msg="{ scope }">
        <span :title="scope.msg" class="text-#F96168 text-ellipsis overflow-hidden whitespace-nowrap">{{ scope.msg
          }}</span>
      </template>
    </uiTable>
  </dataModal>
</template>

<script setup lang="ts">
import dataModal from '@/components/dataModal/dataModal.vue'
import { ref, watch } from 'vue'
import { activeObject, clickPosition } from '../data'
import { clearLastActive } from '..'
import uiTable from '@/components/uiTable/uiTable.vue'
import alarmLevelIcon1 from '@/assets/images/dataCenterLayout/告警-1.png'
import alarmLevelIcon2 from '@/assets/images/dataCenterLayout/告警-2.png'
import alarmLevelIcon3 from '@/assets/images/dataCenterLayout/告警-3.png'

const title = ref('设备名称：调制器主1')

const visible = ref(false)

watch(activeObject, (newVal) => {
  if (newVal && (newVal.name.includes('UPS') || newVal.name.includes('PDG'))) {
    title.value = `设备名称：${newVal.name}`
    getDetail()
    visible.value = true
  } else {
    visible.value = false
  }
})

const alarmLevelIconMap = new Map([
  ['low', alarmLevelIcon1],
  ['mid', alarmLevelIcon2],
  ['high', alarmLevelIcon3],
])

const detail = ref<any>(null)

const alarmColumns = [
  {
    key: 'level',
    width: 50,
    name: '',
  },
  {
    key: 'time',
    width: 150,
    name: '时间',
  },
  {
    key: 'device',
    width: 100,
    name: '设备',
  },
  {
    key: 'msg',
    width: 150,
    name: '告警信息',
  },
]
// 告警信息数据
const alarmList = ref([
  {
    id: 1,
    time: '2025-05-26 12:11:23',
    device: '调制器主1',
    msg: '输入信号切换至备路',
    level: 'low',
  },
  {
    id: 2,
    time: '2025-05-26 12:11:23',
    device: '功放主1',
    msg: '温度越上限告警',
    level: 'mid',
  },
  {
    id: 3,
    time: '2025-05-26 12:11:23',
    device: '中频切换',
    msg: '切换至备路',
    level: 'high',
  },
])

const getDetail = () => {
  detail.value = [
    {
      key: 'model',
      name: '型号',
      value: 'cwdac1002',
    },
    {
      key: 'buyTime',
      name: '购买时间',
      value: '2025-07-17 15:46:32',
    },
    {
      key: 'supplier',
      name: '供应商',
      value: '智能运维平台',
    },
  ]
}

const handleClose = () => {
  clearLastActive()
}
</script>

<style scoped lang="scss">
.detail {
  width: 400px;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;

  .detail-item {
    @apply flex items-end w-full h-40px text-white text-20px;

    .detail-item-value {
      margin-left: 8px;
    }
  }
}
</style>
