<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-05
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-10
-->
<template>
  <Modal title="" v-model:visible="visible" :showCancel="false" confirm-text="关闭" @confirm="handleClose"
    @close="handleClose">

    <!-- Tab页 -->
    <div class="tabs-container">
      <div class="tabs-header">
        <div v-for="(tab, index) in detail" :key="tab.type" class="tab-item"
          :class="{ active: activeTabIndex === index }" @click="switchTab(index)">
          {{ tab.label || tab.type }}
        </div>
      </div>

      <!-- Tab内容 -->
      <div class="tab-content">
        <div v-if="detail && detail[activeTabIndex]" class="detail-info">
          <div class="detail-item" v-for="item in detail[activeTabIndex].data" :key="item.name">
            <span class="label">{{ item.name }}：</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>

  </Modal>
</template>

<script setup lang="ts">
import Modal from '@/components/Modal/Modal.vue'
import { ref, watch } from 'vue'
import { activeObject } from '../data'
import { clearLastActive } from '..'

const title = ref('')
const visible = ref(false)
const activeTabIndex = ref(0)

watch(activeObject, (newVal) => {
  if (newVal && newVal.name.includes('JSQ')) {
    title.value = `设备名称：${newVal.name}`
    getDetail()
    visible.value = true
    activeTabIndex.value = 0 // 重置到第一个tab
  } else {
    visible.value = false
  }
})

const detail = ref<any>(null)

const getDetail = () => {
  detail.value = [
    {
      type: '设计参数',
      label: '设计参数',
      data: [
        {
          name: '口径',
          value: '56',
        },
        {
          name: '增益',
          value: '48',
        },
        {
          name: '工作频率',
          value: '12.5-18 GHz',
        },
        {
          name: '极化方式',
          value: '双极化',
        },
      ]
    },
    {
      type: '安装档案',
      label: '安装档案',
      data: [
        {
          name: '施工单位',
          value: '智能运维平台',
        },
        {
          name: '验收时间',
          value: '2025-07-17 15:46:32',
        },
        {
          name: '安装位置',
          value: '园区A区',
        },
        {
          name: '设备状态',
          value: '正常运行',
        },
      ]
    }
  ]
}

const switchTab = (index: number) => {
  activeTabIndex.value = index
}

const handleClose = () => {
  clearLastActive()
  activeObject.value = null
}
</script>

<style scoped lang="scss">
.tabs-container {
  @apply w-600px;
  font-family: "Inter", "Source Han Sans SC", "PingFang SC", "Microsoft YaHei", sans-serif;

  .tabs-header {
    @apply flex justify-center mb-6;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 80%;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(51, 125, 255, 0.3) 20%, rgba(51, 125, 255, 0.3) 80%, transparent 100%);
    }

    .tab-item {
      @apply px-6 py-3 cursor-pointer text-base font-medium transition-all duration-300 mx-2 rounded-t-lg;
      color: #cfcfcf;
      border: 2px solid transparent;
      border-bottom: none;
      position: relative;
      min-width: 120px;
      text-align: center;
      background: rgba(51, 125, 255, 0.2);

      &:hover {
        color: #337DFF;
        background: rgba(51, 125, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(51, 125, 255, 0.2);
      }

      &.active {
        color: #FFFFFF;
        background: linear-gradient(135deg, rgba(51, 125, 255, 0.9) 0%, rgba(51, 125, 255, 0.7) 100%);
        border-color: rgba(51, 125, 255, 0.8);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(51, 125, 255, 0.3);

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          right: 0;
          height: 2px;
          background: #337DFF;
          border-radius: 0 0 2px 2px;
        }
      }

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .tab-content {
    @apply min-h-32;

    .detail-info {
      @apply space-y-3;

      .detail-item {
        @apply flex items-center justify-between py-3 px-4 rounded-lg;
        background: rgba(51, 125, 255, 0.08);
        border: 1px solid rgba(51, 125, 255, 0.15);
        transition: all 0.2s ease-out;

        &:hover {
          background: rgba(51, 125, 255, 0.12);
          border-color: rgba(51, 125, 255, 0.25);
          transform: translateY(-1px);
        }

        .label {
          @apply text-sm font-medium;
          color: #E6F0FF;
        }

        .value {
          @apply text-sm font-semibold;
          color: #FFFFFF;
        }
      }
    }
  }
}
</style>
