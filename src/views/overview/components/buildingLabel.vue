<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-05
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-12
-->
<template>
  <dataModal :title="title" v-model:visible="visible"
    :style="{ top: `${labelPosition?.y}px`, left: `${labelPosition?.x}px` }" :showClose="false">
    <div class="detail">
      <div class="detail-info">
        <div class="detail-item" v-for="item in detail">
          <span>{{ item.name }}:</span>
          <span class="ml-2">{{ item.value }}</span>
        </div>
      </div>
      <div class="img"></div>
    </div>
  </dataModal>
</template>

<script setup lang="ts">
import dataModal from '@/components/dataModal/dataModal.vue'
import { onMounted, onUnmounted, ref } from 'vue'
import { until } from '@/utils'
import { viewer } from '@/three/viewer'
import { threeJSEvents } from '@/three/evens'
import highlightManager from '@/three/highLight'
import { activeObject } from '../data'
import { Box3, Vector3 } from 'three'
import { wordToScreen } from '@/three/utils'

const title = ref('')

const visible = ref(false)

const detail = ref<any>([])

const labelPosition = ref<any>(null)

const getDetail = () => {
  detail.value = [
    {
      name: '建成时间',
      value: '2025-01-01',
    },
    {
      name: '建筑面积',
      value: '1287㎡',
    },
    {
      name: '用途',
      value: '办公',
    },
  ]
}

let beforeHoverObject: any = null
let checkoutTimeout: number | null = null

const onObjectHover = (e: any) => {
  if (e.object.name === beforeHoverObject?.name) {
    return
  }
  if (checkoutTimeout) {
    clearTimeout(checkoutTimeout)
  }
  beforeHoverObject = e.object
  checkoutTimeout = setTimeout(() => {
    if (e.object.name === beforeHoverObject?.name && beforeHoverObject?.name.includes('ZT')) {
      title.value = e.object.name
      highlightManager.highlightObject(e.object)
      getDetail()
      transformPoint()
      visible.value = true
    } else {
      visible.value = false
    }
  }, 1000)
}

const onObjectLeave = (e: any) => {
  if (checkoutTimeout) {
    clearTimeout(checkoutTimeout)
  }
  title.value = ''
  if (visible.value && beforeHoverObject && activeObject.value?.name !== beforeHoverObject.name) {
    highlightManager.clearObjectHighlight(beforeHoverObject)
  }
  beforeHoverObject = null
  visible.value = false
}

// const onMouseMove = (e: MouseEvent) => {
//   labelPosition.value = {
//     x: e.clientX,
//     y: e.clientY,
//   }
// }

const transformPoint = () => {
  if (!beforeHoverObject) {
    return
  }
  const boundingBox = new Box3().setFromObject(beforeHoverObject)
  const center = new Vector3()
  boundingBox.getCenter(center)
  labelPosition.value = wordToScreen(center)
}

onMounted(async () => {
  until(() => viewer?.isInited).then(() => {
    threeJSEvents.subscribe('object:hover', onObjectHover)
    threeJSEvents.subscribe('object:leave', onObjectLeave)
    threeJSEvents.subscribe('camera:change', transformPoint)
    // window.addEventListener('mousemove', onMouseMove)
  })
})

onUnmounted(() => {
  threeJSEvents.unsubscribe('object:hover', onObjectHover)
  threeJSEvents.unsubscribe('object:leave', onObjectLeave)
  threeJSEvents.unsubscribe('camera:change', transformPoint)
  highlightManager.clearAllHighlights()
  // window.removeEventListener('mousemove', onMouseMove)
})
</script>

<style scoped lang="scss">
.detail {
  @apply flex justify-between items-center w-full text-white mt-13px;
  font-family: Alibaba PuHuiTi;

  .detail-info {
    @apply items-center h-full;

    .detail-item {
      @apply flex items-center h-30px whitespace-nowrap;
    }
  }

  .img {
    @apply w-120px h-80px bg-#002545FF ml-12px;
  }
}
</style>
