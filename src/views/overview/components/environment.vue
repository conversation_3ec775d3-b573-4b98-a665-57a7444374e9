<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-09
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-25
-->
<template>
  <Teleport to=".screen-wrapper">
    <div class="weathers">
      <div class="weather-item" @click="handleToggleTime">
        <Icon :name="timeIcon" :title="time === 'day' ? '切换夜晚' : '切换白天'" />
      </div>
      <view
        :class="['weather-item', { active: weather.active }]"
        :title="weather.active ? '停止' + weather.name : weather.name"
        v-for="weather in weathers"
        @click="handleClick(weather)"
      >
        <Icon :name="weather.icon" />
      </view>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { Rain } from '@/three/rain'
import { Snow } from '@/three/snow'
import Icon from '@/components/Icon/Icon.vue'
import { viewer } from '@/three/viewer'
import { useLayoutStore } from '@/stores/layoutStore'
import { until } from '@/utils'
import { getWeatherStatusApi } from '@/api/station'
import * as THREE from 'three'

dayjs.extend(utc)
dayjs.extend(timezone)

let rain: Rain | null = null
let snow: Snow | null = null
let autoTimeTimer: number | null = null
let weatherTimer: number | null = null
const isManualMode = ref(false) // 是否为手动模式
const isManualWeatherMode = ref(false) // 是否为手动天气模式
const currentWeatherStatus = ref(0) // 当前天气状态：0无雨雪；1雨；2雪

const weathers = ref([
  {
    icon: 'zhongyu-dayu',
    active: false,
    name: '下雨',
    open: () => {
      if (rain !== null) {
        rain.start!()
      } else {
        rain = new Rain()
        rain.start!()
      }
    },
    close: () => {
      if (rain) {
        rain.stop!()
      }
    },
  },
  {
    icon: 'daxue',
    active: false,
    name: '下雪',
    open: () => {
      if (snow !== null) {
        snow.start!()
      } else {
        snow = new Snow()
        snow.start!()
      }
    },
    close: () => {
      if (snow) {
        snow.stop!()
      }
    },
  },
])

const time = ref<'day' | 'night'>('day')

const timeIcon = computed(() => {
  return time.value === 'day' ? 'yewan' : 'qingbaitian'
})

const handleClick = (weather: (typeof weathers.value)[number]) => {
  // 手动点击时进入手动天气模式
  isManualWeatherMode.value = true

  if (weather.active) {
    weather.active = false
    weather.close()
  } else {
    weathers.value.forEach((item) => {
      if (item.active) {
        item.active = false
        item.close()
      }
    })
    weather.active = true
    weather.open()
  }

  // 5分钟后恢复自动天气模式
  setTimeout(
    () => {
      isManualWeatherMode.value = false
    },
    5 * 60 * 1000,
  )
}

// 检查当前北京时间是否为夜晚（19:00-06:00）
const isNightTime = (): boolean => {
  const beijingTime = dayjs().tz('Asia/Shanghai')
  const hour = beijingTime.hour()
  return hour >= 19 || hour < 6
}

// 自动设置白天/黑夜
const autoSetDayNight = () => {
  if (isManualMode.value) return // 手动模式下不自动切换

  const shouldBeNight = isNightTime()
  if (shouldBeNight && time.value === 'day') {
    time.value = 'night'
    toNight()
  } else if (!shouldBeNight && time.value === 'night') {
    time.value = 'day'
    toDay()
  }
}

// 启动自动时间检测
const startAutoTimeDetection = async () => {
  await until(() => viewer?.isInited && !!viewer?.skybox)

  // 立即检查一次
  autoSetDayNight()
  // 每分钟检查一次
  autoTimeTimer = setInterval(autoSetDayNight, 60000)
}

// 停止自动时间检测
const stopAutoTimeDetection = () => {
  if (autoTimeTimer) {
    clearInterval(autoTimeTimer)
    autoTimeTimer = null
  }
}

// 获取天气状态
const fetchWeatherStatus = async () => {
  try {
    const status = await getWeatherStatusApi()
    return Number(status) || 0
  } catch (error) {
    console.error('获取天气状态失败:', error)
    return 0
  }
}

// 根据天气状态自动控制天气效果
const autoControlWeather = (status: number) => {
  if (isManualWeatherMode.value) return // 手动模式下不自动控制

  // 先停止所有天气效果
  weathers.value.forEach((weather) => {
    if (weather.active) {
      weather.active = false
      weather.close()
    }
  })

  // 根据状态启动对应效果
  switch (status) {
    case 1: // 雨
      const rainWeather = weathers.value.find((w) => w.name === '下雨')
      if (rainWeather) {
        rainWeather.active = true
        rainWeather.open()
      }
      break
    case 2: // 雪
      const snowWeather = weathers.value.find((w) => w.name === '下雪')
      if (snowWeather) {
        snowWeather.active = true
        snowWeather.open()
      }
      break
    case 0: // 无雨雪
    default:
      break
  }

  currentWeatherStatus.value = status
}

// 启动自动天气检测
const startAutoWeatherDetection = async () => {
  await until(() => viewer?.isInited)

  // 立即检查一次
  const status = await fetchWeatherStatus()
  autoControlWeather(status)

  // 每30秒检查一次天气状态
  weatherTimer = setInterval(async () => {
    const status = await fetchWeatherStatus()
    if (status !== currentWeatherStatus.value) {
      autoControlWeather(status)
    }
  }, 30000)
}

// 停止自动天气检测
const stopAutoWeatherDetection = () => {
  if (weatherTimer) {
    clearInterval(weatherTimer)
    weatherTimer = null
  }
}

const nightRoomLightsPos = [
  [-14.413, 5.711, -11.957],
  [-22.064, 6.46, -20.021],
  [-25.89, 5.134, -15.293],
  [-29.803, 5.072, -12.923],
  [-32.151, 5.207, -10.752],
  [-34.945, 8.093, -10.159],
  [-34.658, 11.609, -9.603],
  [-34.658, 14.419, -9.675],
  [-27.666, 15.708, -7.26],
  [-35.082, 6.088, -4.728],
  [-25.858, 9.654, -2.307],
  [-21.73, 5.158, -8.473],
]

let nightRoomLights: THREE.PointLight[] = []

const toNight = async () => {
  const isRain = weathers.value[0].active
  // 雨天状态下不切换天空盒,使用雨天天空盒
  if (!isRain) {
    await viewer.skybox.setSkyBox({
      name: 'night',
      url: 'skybox/skybox-night.exr',
    })
  }
  viewer.sunLight!.intensity = 5
  viewer.sunLight!.color.set(0xcccccc)
  viewer.sunLight!.position.set(5.267389289682579, 250, 20.61818091545023)
  viewer.renderer.toneMappingExposure = !isRain ? 0.2 : 0.1
  nightRoomLightsPos.forEach((point) => {
    const pointLight = new THREE.PointLight(0xffffff, 150, 10, 1)
    pointLight.position.set(point[0], point[1], point[2])
    // pointLight.castShadow = true
    // pointLight.shadow.mapSize.width = 2048
    // pointLight.shadow.mapSize.height = 2048
    // pointLight.shadow.bias = -0.001
    // // 调整点光源阴影强度，使阴影更浅
    // pointLight.shadow.intensity = 0.5
    viewer.scene.add(pointLight)
    nightRoomLights.push(pointLight)
  })
}

const toDay = async () => {
  const isRain = weathers.value[0].active
  // 雨天状态下不切换天空盒,使用雨天天空盒
  if (!isRain) {
    await viewer.skybox.setSkyBox({
      name: 'day',
      url: 'skybox/skybox-day.exr',
    })
  }
  viewer.sunLight!.intensity = 10
  viewer.sunLight!.color.set(0xffffff)
  viewer.sunLight!.position.set(5.267389289682579, 250, 40.61818091545023)
  // viewer.scene.environment = null
  viewer.renderer.toneMappingExposure = !isRain ? 1 : 0.3
  nightRoomLights.forEach((light) => {
    viewer.scene.remove(light)
  })
  nightRoomLights = []
}
const handleToggleTime = () => {
  // 手动切换时进入手动模式
  isManualMode.value = true

  time.value = time.value === 'day' ? 'night' : 'day'
  if (time.value === 'day') {
    toDay()
  } else {
    toNight()
  }

  // 5分钟后恢复自动模式
  setTimeout(
    () => {
      isManualMode.value = false
    },
    5 * 60 * 1000,
  )
}

const layoutStore = useLayoutStore()

const right = computed(() => {
  return layoutStore.showRightPanel ? '650px' : '100px'
})

onMounted(() => {
  // 启动自动时间检测
  startAutoTimeDetection()
  // 启动自动天气检测
  startAutoWeatherDetection()
})

onUnmounted(() => {
  // 停止自动时间检测
  stopAutoTimeDetection()
  // 停止自动天气检测
  stopAutoWeatherDetection()

  weathers.value.forEach((weather) => {
    if (weather.active) {
      weather.close()
    }
  })
  rain = null
  snow = null
  toDay()
})
</script>

<style scoped lang="scss">
.weathers {
  @apply fixed top-300px flex flex-col gap-16px z-9999;
  right: v-bind('right');
  transition: right 0.5s ease;

  .weather-item {
    @apply flex w-50px h-50px flex justify-center items-center rounded-6px cursor-pointer text-30px;
    background-color: rgba(49, 143, 229, 0.5);
    border: 1px solid rgba(27, 150, 255, 1);

    &:hover {
      background-color: rgba(49, 143, 229, 0.9);
    }
  }

  .active {
    background-color: rgba(49, 143, 229, 0.9);
  }
}
</style>
