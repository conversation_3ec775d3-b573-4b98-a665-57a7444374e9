/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-02
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-21
 */
import { threeJSEvents } from '@/three/evens'
import { navigate } from '@/three/navigate'
import { viewer } from '@/three/viewer'
import { until } from '@/utils'
import { activeObject, clickPosition } from './data'
import type { Mesh, Vector3 } from 'three'
import highlightManager from '@/three/highLight'
import { wordToScreen } from '@/three/utils'

let clickPoint: Vector3 | null = null
// 场景坐标转屏幕坐标
const transformPoint = () => {
  if (!clickPoint) {
    clickPosition.value = null
    return
  }
  clickPosition.value = wordToScreen(clickPoint)
}

// 清除激活的对象
export const clearLastActive = () => {
  if (activeObject.value) {
    highlightManager.clearObjectHighlight(activeObject.value)
    clickPosition.value = null
    threeJSEvents.unsubscribe('camera:change', transformPoint)
  }
}

// 设置激活对象
export const setActiveObject = (object: Mesh, point: Vector3) => {
  clearLastActive()
  activeObject.value = object
  highlightManager.highlightObject(object)
  clickPoint = point
  transformPoint()
  threeJSEvents.subscribe('camera:change', transformPoint)
}

// 对象点击事件
const onObjectClick = (e: any) => {
  console.log('object:dblclick', e)
  const { object } = e
  if (object.name === activeObject.value?.name) {
    return
  }
  if (object.name.includes('UPS') || object.name.includes('JSQ') || object.name.includes('PDG')) {
    setActiveObject(object, e.point)
  }
}

export const locate = () => {
  // navigate.flyTo(
  //   [10.30385786642376, 40.73821424957181, 30.235476046178903],
  //   [-8.740362510816375, 0.22452448523837876, -7.67095172915428],
  // )
  navigate.flyToWithRotation(
    [9.447204584351923, 20.622400960487912, 13.39087348926837],
    [-0.731206464495463, 0.7854837219392612, 0.5653264146544836],
  )
}

export const initScene = async () => {
  until(() => viewer?.isInited).then(() => {
    locate()
    threeJSEvents.subscribe('object:dblclick', onObjectClick)
  })
}

export const clearScene = () => {
  threeJSEvents.unsubscribe('object:dblclick', onObjectClick)
  threeJSEvents.unsubscribe('camera:change', transformPoint)
  clearLastActive()
}
