<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-05
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-12
-->
<template>
  <dataModal :title="title" v-model:visible="visible"
    :style="{ top: `${labelPosition?.y}px`, left: `${labelPosition?.x}px` }">
    <div class="detail">
      <div class="detail-info">
        <div class="detail-item" v-for="item in detail">
          <span>{{ item.name }}</span>
          <span class="ml-2">{{ item.value }}</span>
        </div>
      </div>
      <div class="img"></div>
    </div>
  </dataModal>
</template>

<script setup lang="ts">
import dataModal from '@/components/dataModal/dataModal.vue'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useCesiumStore } from '@/stores/cesiumStore'
import { until } from '@/utils'
import { currentLevel } from '../data'

const title = ref('')

const visible = ref(false)

const detail = ref<any>([])

const labelPosition = ref<any>(null)

let currentCity = null

watch(() => currentLevel.value, (val) => {
  if (val !== 'city') {
    clear()
  }
})

const getDetail = () => {
  detail.value = [
    {
      name: '浙江',
      value: '101.9',
    },
    {
      name: '交通',
      value: '93.0',
    }
  ]
}

// cesium场景坐标转屏幕坐标
const transformPoint = () => {
  if (!clickPointPosition) return
  // Cesium坐标转屏幕坐标
  const scene = window.viewer && window.viewer.scene
  if (!scene) return

  // 使用Cesium的SceneTransforms.worldToWindowCoordinates
  const cartesian = clickPointPosition
  const windowPosition = Cesium.SceneTransforms.worldToWindowCoordinates(scene, cartesian)

  if (windowPosition) {
    // 应用缩放插件的反向转换
    const scale = window.cesiumTransformTool?.getCurrentScale() || 1
    if (scale !== 1) {
      const margin = window.cesiumTransformTool?.getCurrentMargin() || [0, 0]
      // 获取Cesium容器的位置信息
      const cesiumContainer = document.querySelector('.cesium-map')
      if (cesiumContainer) {
        const rect = cesiumContainer.getBoundingClientRect()
        // 转换回缩放插件的坐标系
        const adjustedX = windowPosition.x * scale + rect.left - margin[1]
        const adjustedY = windowPosition.y * scale + rect.top - margin[0]
        labelPosition.value = { x: adjustedX, y: adjustedY }
        return
      }
    }

    // 如果没有缩放或转换失败，使用原始坐标
    labelPosition.value = { x: windowPosition.x, y: windowPosition.y }
  } else {
    labelPosition.value = null
  }
}

let clickPointPosition = null
const onPickCity = (movement: any) => {
  const { name, position } = movement.detail
  clickPointPosition = position
  title.value = `${name}广播调频覆盖点`
  currentCity = name
  getDetail()
  visible.value = true
  // 初次弹窗时立即转换一次
  transformPoint()
}

let removePostRender = null
onMounted(async () => {
  window.addEventListener('cityAntennaClick', onPickCity as EventListener)
  // 监听Cesium场景移动，实时更新屏幕坐标
  const cesiumStore = useCesiumStore()
  if (!cesiumStore.sceneLoaded) {
    await until(() => cesiumStore.sceneLoaded)
  }
  const scene = window.viewer.scene
  if (scene) {
    removePostRender = () => scene.postRender.removeEventListener(transformPoint)
  }
})

onUnmounted(() => {
  window.removeEventListener('cityAntennaClick', onPickCity as EventListener)
  if (removePostRender) removePostRender()
})

const clear = () => {
  visible.value = false
  labelPosition.value = null
  currentCity = null
  clickPointPosition = null
  detail.value = []
}
defineExpose({
  clear
})
</script>

<style scoped lang="scss">
.detail {
  @apply flex justify-between items-center w-full text-white mt-13px;
  font-family: Alibaba PuHuiTi;

  .detail-info {
    @apply items-center h-full;

    .detail-item {
      @apply flex items-center h-30px;
    }
  }

  .img {
    @apply w-120px h-80px bg-#002545FF;
  }
}
</style>
