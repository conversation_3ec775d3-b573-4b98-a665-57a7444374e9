<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-05
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-12
-->
<template>
  <dataModal :title="title" v-model:visible="visible"
    :style="{ top: `${labelPosition?.y}px`, left: `${labelPosition?.x}px` }">
    <div class="detail">
      <div class="detail-info">
        <div class="detail-info-title">覆盖点位简介:</div>
        <div class="detail-info-content">简介简介简介简介简介简介简介简介简介简介简介简介简介简介</div>
      </div>
      <div class="img"></div>
    </div>
    <uiTable :columns="alarmColumns" :data="tableData" mt-20px></uiTable>
  </dataModal>
</template>

<script setup lang="ts">
import dataModal from '@/components/dataModal/dataModal.vue'
import { onMounted, onUnmounted, ref } from 'vue'
import uiTable from '@/components/uiTable/uiTable.vue'
import { useCesiumStore } from '@/stores/cesiumStore'
import { until } from '@/utils'

const title = ref('')

const visible = ref(false)

const detail = ref<any>(null)

const labelPosition = ref<any>(null)

let currentProvince = ''

const alarmColumns = [
  {
    key: 'program',
    width: 200,
    name: '节目',
  },
  {
    key: 'power',
    width: 200,
    name: '覆盖功率',
  }
]
// 节目信息数据
const tableData = ref<any[]>([])

const getDetail = () => {
  detail.value = [
    {
      key: 'model',
      name: '型号',
      value: 'cwdac1002',
    },
    {
      key: 'buyTime',
      name: '购买时间',
      value: '2025-07-17 15:46:32',
    },
    {
      key: 'supplier',
      name: '供应商',
      value: '智能运维平台',
    },
  ]
  tableData.value = [
    {
      program: currentProvince + '标清',
      power: '18 dBW',
    },
    {
      program: currentProvince + '高清',
      power: '22 dBW',
    },
    {
      program: currentProvince + '4K',
      power: '19 dBW',
    },
  ]
}

// cesium场景坐标转屏幕坐标
const transformPoint = () => {
  if (!clickPointPosition) return
  // Cesium坐标转屏幕坐标
  const scene = window.viewer && window.viewer.scene
  if (!scene) return

  // 使用Cesium的SceneTransforms.worldToWindowCoordinates
  const cartesian = clickPointPosition
  const windowPosition = Cesium.SceneTransforms.worldToWindowCoordinates(scene, cartesian)

  if (windowPosition) {
    // 应用缩放插件的反向转换
    const scale = window.cesiumTransformTool?.getCurrentScale() || 1
    if (scale !== 1) {
      const margin = window.cesiumTransformTool?.getCurrentMargin() || [0, 0]
      // 获取Cesium容器的位置信息
      const cesiumContainer = document.querySelector('.cesium-map')
      if (cesiumContainer) {
        const rect = cesiumContainer.getBoundingClientRect()
        // 转换回缩放插件的坐标系
        const adjustedX = windowPosition.x * scale + rect.left - margin[1]
        const adjustedY = windowPosition.y * scale + rect.top - margin[0]
        labelPosition.value = { x: adjustedX, y: adjustedY }
        return
      }
    }

    // 如果没有缩放或转换失败，使用原始坐标
    labelPosition.value = { x: windowPosition.x, y: windowPosition.y }
  } else {
    labelPosition.value = null
  }
}

let clickPointPosition = null
const onPickProvince = (movement: any) => {
  const { name, position } = movement.detail
  clickPointPosition = position
  title.value = `${name}覆盖信息`
  currentProvince = name
  getDetail()
  visible.value = true
  // 初次弹窗时立即转换一次
  transformPoint()
}
const onPickProvinceEntity = () => {
  visible.value = false
  title.value = ''
  detail.value = []
  tableData.value = []
}


let removePostRender = null
onMounted(async () => {
  window.addEventListener('provinceAntennaClick', onPickProvince as EventListener)
  window.addEventListener('provinceClick', onPickProvinceEntity as EventListener)
  // 监听Cesium场景移动，实时更新屏幕坐标
  const cesiumStore = useCesiumStore()
  if (!cesiumStore.sceneLoaded) {
    await until(() => cesiumStore.sceneLoaded)
  }
  const scene = window.viewer.scene
  if (scene) {
    // Cesium的postRender事件
    const handler = scene.postRender.addEventListener(transformPoint)
    removePostRender = () => scene.postRender.removeEventListener(transformPoint)
  }
})

onUnmounted(() => {
  window.removeEventListener('provinceAntennaClick', onPickProvince as EventListener)
  window.removeEventListener('provinceClick', onPickProvinceEntity as EventListener)
  if (removePostRender) removePostRender()
})
</script>

<style scoped lang="scss">
.detail {
  @apply flex justify-between items-end w-400px text-white;
  font-family: Alibaba PuHuiTi;

  .detail-info {
    .detail-info-title {
      @apply text-20px my-20px;
    }

    .detail-info-content {
      @apply w-200px leading-30px font-300;
      @include text-overflow;
    }
  }

  .img {
    @apply w-120px h-80px bg-#002545FF;
  }
}
</style>
