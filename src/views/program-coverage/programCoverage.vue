<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-19
 * @LastEditors: 余承
 * @LastEditTime: 2025-08-21
-->
<template>
  <Container title="节目覆盖数据">
    <div class="h-[1150px] pl-6 pr-2.5">
      <div>
        <div class="h-[70px] flex items-center border-b-1 border-b-solid border-white/74">
          <div v-for="item in columns" :class="[`${!item.width ? 'flex-1' : ''}`, 'text-center text-xl']"
            :style="{ width: item.width ?? 'auto' }">
            {{ item.title }}
          </div>
        </div>
        <div class="h-[1080px] w-full">
          <LoopScroll :dataSource="data" itemKey="index" :speed="0.3">
            <template #default="{ item }">
              <div class="flex h-10 items-center border-b-1 border-b-solid border-[#D8E3EE]/10 text-lg">
                <div :style="{ width: '80px' }" class="text-center">{{ item.index }}</div>
                <div class="flex-1 text-center line-clamp-1">{{ item.area }}</div>
                <div class="flex-1 text-center line-clamp-1">{{ item.program }}</div>
                <div class="flex-1 text-center text-[#99F17A]">{{ item.eirp }}</div>
              </div>
            </template>
          </LoopScroll>
        </div>
      </div>
    </div>
  </Container>

  <Teleport to=".screen-wrapper">
    <div class="satellite-container"></div>
  </Teleport>

  <!-- 预加载，防止闪烁 -->
  <div class="absolute -left-9999px">
    <img v-for="item in 7" :key="item" :src="getAssetsImage(`satellite_${item}.png`, 'program-coverage')" />
  </div>
  <Teleport to=".screen-wrapper">
    <Icon v-if="currentLevel === 'city'" name="fanhui" class="fuwei  top-170px! text-20px!"
      :style="{ left: layoutStore.showLeftPanel ? '640px' : '50px' }" title="返回" @click="backProvince" />
    <Icon name="fuwei" class="fuwei" title="复位" :style="{ left: layoutStore.showLeftPanel ? '640px' : '50px' }"
      @click="fuwei" />
  </Teleport>
  <provinceLabel />
  <cityLabel />
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { initScene, clearScene, backProvince, fuwei } from './index'
import { LoopScroll } from '@joyday/vue-loop-scroll'
import Container from '@/components/Container/Container.vue'
import provinceLabel from './components/provinceLabel.vue'
import cityLabel from './components/cityLabel.vue'
import { currentLevel } from './data'
import { getAssetsImage } from '@/utils/common'
import Icon from '@/components/Icon/Icon.vue'
import { useLayoutStore } from '@/stores/layoutStore'

const layoutStore = useLayoutStore()

const columns = [
  {
    title: '序号',
    key: 'index',
    width: '80px',
  },
  {
    title: '覆盖区域',
    key: 'area',
  },
  {
    title: '节目',
    key: 'program',
  },
  {
    title: '辐射功率(EIRP)',
    key: 'eirp',
  },
]

const data = Array.from({ length: 100 }, (_, index) => ({
  index: index + 1,
  area: `覆盖区域${index + 1}`,
  program: `节目${index + 1}`,
  eirp: `${100 + index * 20}W`,
}))

onMounted(() => {
  initScene()
  window.addEventListener('provinceClick')
})

onUnmounted(() => {
  clearScene()
})
</script>

<style scoped lang="scss">
:deep(.scroll-loop-track) {
  .scroll-loop-item:nth-child(even)>div {
    background-color: rgba(27, 130, 183, 0.12) !important;
  }

  .scroll-loop-item:nth-child(odd)>div {
    background-color: rgba(27, 130, 183, 0.22) !important;
  }
}

.satellite-container {
  position: fixed;
  width: 540px;
  height: 297px;
  top: 250px;
  right: 600px;
  animation: satellite 3s linear infinite;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: left top;
  z-index: 999;
}

.fuwei {
  @apply fixed top-240px flex justify-center items-center rounded-full text-36px text-white bg-#1ea2ff88 w-54px h-54px z-10 cursor-pointer transition-all duration-300;

  &:hover {
    @apply bg-#1ea2ff;
  }
}

@keyframes satellite {
  0% {
    background-image: url('@/assets/images/program-coverage/satellite_1.png');
  }

  14% {
    background-image: url('@/assets/images/program-coverage/satellite_2.png');
  }

  28% {
    background-image: url('@/assets/images/program-coverage/satellite_3.png');
  }

  42% {
    background-image: url('@/assets/images/program-coverage/satellite_4.png');
  }

  56% {
    background-image: url('@/assets/images/program-coverage/satellite_5.png');
  }

  70% {
    background-image: url('@/assets/images/program-coverage/satellite_6.png');
  }

  84% {
    background-image: url('@/assets/images/program-coverage/satellite_7.png');
  }

  100% {
    background-image: url('@/assets/images/program-coverage/satellite_1.png');
  }
}
</style>
