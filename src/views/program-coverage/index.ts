import provinceGeoJson from '@/data/geojson/省份总体.json'
import { useCesiumStore } from '@/stores/cesiumStore'
import { sleep, until } from '@/utils'
import { hexToCesiumColor } from '@/utils/cesium'
import { createDiscreteApi } from 'naive-ui'
import { currentLevel } from './data'
import * as turf from '@turf/turf'
/**
 * 创建省份区域四色图
 */
const createProvinceArea = async () => {
  const cesiumStore = useCesiumStore()
  if (!cesiumStore.sceneLoaded) {
    await until(() => cesiumStore.sceneLoaded)
  }
  await sleep(500)
  const scene = window.viewer.scene

  // 创建数据源
  const dataSource = new Cesium.GeoJsonDataSource()

  // 加载GeoJSON数据
  const loadedDataSource = await dataSource.load(provinceGeoJson, {
    stroke: Cesium.Color.RED,
    fill: Cesium.Color.TRANSPARENT,
    strokeWidth: 5,
  })

  // 将数据源添加到viewer
  window.viewer.dataSources.add(loadedDataSource)
  // 获取所有实体
  const entities = dataSource.entities.values

  // 用于存储每个省份的最大面积和对应的实体信息
  const provinceMaxAreaMap = new Map<
    string,
    {
      entity: any
      area: number
      positions: Cesium.Cartesian3[]
      centerX: number
      centerY: number
      centerZ: number
    }
  >()

  // 创建科技蓝渐变材质 - 使用标准的ColorMaterialProperty
  const terrainMaterial = new Cesium.ColorMaterialProperty(
    Cesium.Color.fromCssColorString('#337DFF').withAlpha(0.01),
  )

  // 创建底部边缘高亮材质
  const edgeHighlightMaterial = new Cesium.ColorMaterialProperty(
    Cesium.Color.fromCssColorString('#25CCF7'),
  )

  // 为dataSource以外的区域添加暗色半透明遮罩
  const darkOverlay = new Cesium.Entity({
    rectangle: {
      coordinates: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90), // 全球范围
      material: new Cesium.ColorMaterialProperty(new Cesium.Color(0.05, 0.05, 0.1, 0.7)), // 暗色半透明
      height: 0,
      outline: false,
    },
  })
  window.viewer.entities.add(darkOverlay)

  // 为每个省份应用颜色，并收集面积信息
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]

    if (entity.polygon) {
      const provinceName = entity.properties?.name?.getValue() || `未命名省份${i}`

      // 设置多边形填充为地形贴图
      entity.polygon.material = terrainMaterial

      // 设置多边形的侧面材质（增强立体感）- 注意：如果extrudedMaterial不被支持，可以直接使用material
      try {
        // @ts-ignore - 某些Cesium版本支持extrudedMaterial
        entity.polygon.extrudedMaterial = new Cesium.ColorMaterialProperty(
          Cesium.Color.fromCssColorString('#1B6AE1').withAlpha(0.95),
        )
      } catch (e) {
        console.log('extrudedMaterial不被支持，使用标准material')
      }

      // 添加立体效果 - 设置挤出高度
      entity.polygon.extrudedHeight = new Cesium.ConstantProperty(0) // 设置挤出高度为100km

      // 关闭所有轮廓
      entity.polygon.outline = new Cesium.ConstantProperty(false)

      // 设置多边形的阴影效果
      entity.polygon.shadows = new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED)

      // 创建一个顶面轮廓实体（同样的多边形但没有挤出）
      const outlineEntity = new Cesium.Entity({
        polygon: new Cesium.PolygonGraphics({
          hierarchy: entity.polygon.hierarchy,
          height: new Cesium.ConstantProperty(0), // 与挤出高度相同
          material: Cesium.Color.TRANSPARENT,
          outline: true,
          outlineColor: Cesium.Color.fromCssColorString('#ffffff'), // 青色高亮边缘
          outlineWidth: 3,
        }),
      })
      window.viewer.entities.add(outlineEntity)

      // 使用Polyline绘制边界轮廓，解决Windows平台上outlineWidth不生效的问题
      if (entity.polygon.hierarchy) {
        const hierarchy = entity.polygon.hierarchy.getValue()
        if (hierarchy && hierarchy.positions) {
          const outlinePolyline = new Cesium.Entity({
            polyline: new Cesium.PolylineGraphics({
              positions: hierarchy.positions,
              width: 2,
              material: Cesium.Color.fromCssColorString('#ffffff'),
              clampToGround: false,
              shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
            }),
            position: new Cesium.ConstantPositionProperty(new Cesium.Cartesian3(0, 0, 0)),
          })
          window.viewer.entities.add(outlinePolyline)
        }
      }

      // 额外添加一个顶部边缘发光线
      // if (entity.polygon.hierarchy) {
      //   const hierarchy = entity.polygon.hierarchy.getValue()
      //   if (hierarchy && hierarchy.positions) {
      //     const topEdgeEntity = new Cesium.Entity({
      //       polyline: new Cesium.PolylineGraphics({
      //         positions: hierarchy.positions,
      //         width: new Cesium.ConstantProperty(4),
      //         material: new Cesium.PolylineGlowMaterialProperty({
      //           glowPower: new Cesium.ConstantProperty(0.1),
      //           taperPower: new Cesium.ConstantProperty(0.2),
      //           color: Cesium.Color.fromCssColorString('#00FFFF'),
      //         }),
      //         // 注意：PolylineGraphics可能不支持height属性
      //         // @ts-ignore - 尝试设置高度，如果不支持会被忽略
      //         height: new Cesium.ConstantProperty(100000), // 与挤出高度相同
      //         shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
      //       }),
      //     })
      //     window.viewer.entities.add(topEdgeEntity)
      //   }
      // }

      // 创建底部边缘高亮实体
      // if (entity.polygon.hierarchy) {
      //   const hierarchy = entity.polygon.hierarchy.getValue()
      //   if (hierarchy && hierarchy.positions) {
      //     // 创建底部边缘高亮实体 - 使用预定义的edgeHighlightMaterial
      //     const bottomEdgeEntity = new Cesium.Entity({
      //       polyline: new Cesium.PolylineGraphics({
      //         positions: hierarchy.positions,
      //         width: new Cesium.ConstantProperty(5), // 增加线宽
      //         material: new Cesium.PolylineGlowMaterialProperty({
      //           glowPower: new Cesium.ConstantProperty(0.15), // 减小glowPower增强发光效果
      //           taperPower: new Cesium.ConstantProperty(0.3), // 减小taperPower增强发光效果
      //           color: Cesium.Color.fromCssColorString('#00FFFF'), // 移除透明度以增强可见性
      //         }),
      //         clampToGround: new Cesium.ConstantProperty(true),
      //         shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
      //       }),
      //     })

      //     // 额外创建一个底部边缘实体，使用edgeHighlightMaterial
      //     const bottomEdgeHighlightEntity = new Cesium.Entity({
      //       polyline: new Cesium.PolylineGraphics({
      //         positions: hierarchy.positions,
      //         width: new Cesium.ConstantProperty(8), // 稍细一些的线宽
      //         material: edgeHighlightMaterial,
      //         clampToGround: new Cesium.ConstantProperty(true),
      //         shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
      //       }),
      //     })
      //     window.viewer.entities.add(bottomEdgeHighlightEntity)
      //     window.viewer.entities.add(bottomEdgeEntity)
      //   }
      // }

      // 设置多边形高度为0，从地面开始挤出
      entity.polygon.height = new Cesium.ConstantProperty(0)

      // 计算多边形面积和中心点
      if (entity.properties && entity.properties.name && entity.polygon.hierarchy) {
        const polyPositions = entity.polygon.hierarchy.getValue().positions
        if (polyPositions && polyPositions.length > 0) {
          // 使用 turf 计算几何中心点（质心）
          const center = calculatePolygonCenterWithTurf(polyPositions)
          const centerX = center.x
          const centerY = center.y
          const centerZ = center.z

          // 计算多边形面积
          const area = calculatePolygonArea(polyPositions)

          // 检查是否已有该省份的面积记录，如果没有或当前面积更大，则更新
          if (
            !provinceMaxAreaMap.has(provinceName) ||
            area > provinceMaxAreaMap.get(provinceName)!.area
          ) {
            provinceMaxAreaMap.set(provinceName, {
              entity,
              area,
              positions: polyPositions,
              centerX,
              centerY,
              centerZ,
            })
          }
        }
      }
    }
  }

  // 只为每个省份的最大面积区域创建标签
  provinceMaxAreaMap.forEach((provinceInfo, provinceName) => {
    const { area, centerX, centerY, centerZ } = provinceInfo

    // 只为面积大于阈值的省份添加标签
    // 设置阈值为2万平方公里
    if (area > 20000) {
      // 创建一个单一的实体容器，包含省份名称和天线图标
      const containerEntity = window.viewer.entities.add({
        position: new Cesium.Cartesian3(centerX, centerY, centerZ), // 在多边形上方显示
        name: provinceName, // 设置实体名称为省份名称
        // 省份名称标签
        label: new Cesium.LabelGraphics({
          text: new Cesium.ConstantProperty(provinceName),
          font: new Cesium.ConstantProperty('18px bold 隶书'),
          fillColor: new Cesium.ConstantProperty(Cesium.Color.YELLOW),
          outlineColor: new Cesium.ConstantProperty(Cesium.Color.BLACK),
          outlineWidth: new Cesium.ConstantProperty(3),
          style: new Cesium.ConstantProperty(Cesium.LabelStyle.FILL_AND_OUTLINE),
          verticalOrigin: new Cesium.ConstantProperty(Cesium.VerticalOrigin.TOP),
          horizontalOrigin: new Cesium.ConstantProperty(Cesium.HorizontalOrigin.CENTER),
          pixelOffset: new Cesium.ConstantProperty(new Cesium.Cartesian2(0, -85)), // 减小向上偏移距离
          disableDepthTestDistance: new Cesium.ConstantProperty(Number.POSITIVE_INFINITY), // 禁用深度测试
          eyeOffset: new Cesium.ConstantProperty(new Cesium.Cartesian3(0, 0, 0)), // 调整偏移
          // scaleByDistance: new Cesium.ConstantProperty(
          //   new Cesium.NearFarScalar(1.5e2, 1.6, 1.5e7, 0.7), // 根据距离缩放
          // ),
        }),
        // 天线图标
        billboard: new Cesium.BillboardGraphics({
          image: new Cesium.ConstantProperty(
            new URL('@/assets/images/program-coverage/antenna.png', import.meta.url).href,
          ),
          scale: new Cesium.ConstantProperty(0.7),
          horizontalOrigin: new Cesium.ConstantProperty(Cesium.HorizontalOrigin.CENTER),
          verticalOrigin: new Cesium.ConstantProperty(Cesium.VerticalOrigin.BOTTOM),
          pixelOffset: new Cesium.ConstantProperty(new Cesium.Cartesian2(0, 0)), // 减小向下偏移距离
          disableDepthTestDistance: new Cesium.ConstantProperty(Number.POSITIVE_INFINITY), // 禁用深度测试
          eyeOffset: new Cesium.ConstantProperty(new Cesium.Cartesian3(0, 0, 0)), // 调整偏移
          // 确保图片完全不透明
          color: new Cesium.ConstantProperty(Cesium.Color.WHITE), // 使用白色确保图片原始颜色
          // scaleByDistance: new Cesium.ConstantProperty(
          //   new Cesium.NearFarScalar(1.5e2, 1.4, 1.5e7, 0.7), // 根据距离缩放
          // ),
        }),
      })
      // 添加用户数据
      if (containerEntity) {
        // @ts-ignore
        containerEntity.userData = {
          type: 'provinceAntenna',
        }
      }
    }
  })

  // 增强阴影效果
  // scene.shadowMap.enabled = true
  // scene.shadowMap.darkness = 0.5
  // scene.shadowMap.softShadows = true
  // scene.shadowMap.size = 4096

  // 创建一个方向光源来增强阴影效果
  const sunPosition = Cesium.Cartesian3.fromDegrees(100, 30, 20000000) // 从东南方向照射

  // 计算光照方向 - 从太阳位置指向地球中心
  const toEarthCenter = new Cesium.Cartesian3()
  Cesium.Cartesian3.subtract(Cesium.Cartesian3.ZERO, sunPosition, toEarthCenter)
  const lightDirection = new Cesium.Cartesian3()
  Cesium.Cartesian3.normalize(toEarthCenter, lightDirection)

  // 设置场景光照
  scene.light = new Cesium.SunLight({
    color: Cesium.Color.WHITE,
    intensity: 2.0,
  })

  // 设置光照以增强立体效果
  scene.globe.enableLighting = true
  scene.globe.dynamicAtmosphereLighting = true
  scene.globe.dynamicAtmosphereLightingFromSun = true

  // 增强环境光和反射
  scene.globe.baseColor = Cesium.Color.fromCssColorString('#1B1D21')
  scene.globe.showGroundAtmosphere = true

  // 视角定位到中国
  // window.viewer.flyTo(dataSource, {
  //   duration: 2.5,
  //   offset: new Cesium.HeadingPitchRange(Cesium.Math.toRadians(0), Cesium.Math.toRadians(-65), 1e7),
  // })
  const position = {
    x: -5803145.117801914,
    y: 15171168.48261424,
    z: 2471694.6136358012,
  }
  const direction = {
    x: 0.37307660057388037,
    y: -0.9187656835714296,
    z: 0.129164502847187,
  }

  // 使用给定的 position 和 direction 进行飞行定位
  const destination = new Cesium.Cartesian3(position.x, position.y, position.z)
  const directionVector = new Cesium.Cartesian3(direction.x, direction.y, direction.z)
  // 计算与地表法线一致的 up 向量，保证相机姿态正确
  const upVector = Cesium.Ellipsoid.WGS84.geodeticSurfaceNormal(destination, new Cesium.Cartesian3())

  scene.camera.flyTo({
    destination,
    orientation: {
      direction: directionVector,
      up: upVector,
    },
    duration: 2.0,
  })
}

/**
 * 使用 turf 计算多边形几何中心点
 * @param positions 多边形顶点坐标数组
 * @returns 几何中心点坐标
 */
const calculatePolygonCenterWithTurf = (positions: Cesium.Cartesian3[]): Cesium.Cartesian3 => {
  if (positions.length < 3) {
    return new Cesium.Cartesian3(0, 0, 0)
  }

  try {
    // 将 Cesium.Cartesian3 坐标转换为经纬度坐标
    const coordinates: [number, number][] = []
    for (const position of positions) {
      const cartographic = Cesium.Cartographic.fromCartesian(position)
      const longitude = Cesium.Math.toDegrees(cartographic.longitude)
      const latitude = Cesium.Math.toDegrees(cartographic.latitude)
      coordinates.push([longitude, latitude])
    }

    // 创建 GeoJSON 多边形
    const polygon = turf.polygon([coordinates])

    // 使用 turf 的 centerOfMass 计算质心
    const centerOfMass = turf.centerOfMass(polygon)

    // 将质心坐标转换回 Cesium.Cartesian3
    const centerLon = centerOfMass.geometry.coordinates[0]
    const centerLat = centerOfMass.geometry.coordinates[1]

    // 使用平均高度作为 Z 坐标
    let centerHeight = 0
    for (const position of positions) {
      const cartographic = Cesium.Cartographic.fromCartesian(position)
      centerHeight += cartographic.height
    }
    centerHeight /= positions.length

    return Cesium.Cartesian3.fromDegrees(centerLon, centerLat, centerHeight)
  } catch (error) {
    console.warn('使用 turf 计算中心点失败，回退到简单平均值:', error)
    // 回退到简单平均值计算
    let centerX = 0,
      centerY = 0,
      centerZ = 0
    for (const position of positions) {
      centerX += position.x
      centerY += position.y
      centerZ += position.z
    }
    return new Cesium.Cartesian3(
      centerX / positions.length,
      centerY / positions.length,
      centerZ / positions.length,
    )
  }
}

/**
 * 计算多边形面积
 * @param positions 多边形顶点坐标数组
 * @returns 面积大小
 */
const calculatePolygonArea = (positions: Cesium.Cartesian3[]): number => {
  if (positions.length < 3) {
    return 0
  }

  // 使用简化的方法估算面积 - 计算多边形的边界框面积
  let minX = Number.POSITIVE_INFINITY
  let minY = Number.POSITIVE_INFINITY
  let maxX = Number.NEGATIVE_INFINITY
  let maxY = Number.NEGATIVE_INFINITY

  // 转换为地理坐标
  const ellipsoid = Cesium.Ellipsoid.WGS84

  for (let i = 0; i < positions.length; i++) {
    const cartographic = ellipsoid.cartesianToCartographic(positions[i])
    const lon = Cesium.Math.toDegrees(cartographic.longitude)
    const lat = Cesium.Math.toDegrees(cartographic.latitude)

    minX = Math.min(minX, lon)
    minY = Math.min(minY, lat)
    maxX = Math.max(maxX, lon)
    maxY = Math.max(maxY, lat)
  }

  // 计算边界框的宽度和高度（度）
  const width = maxX - minX
  const height = maxY - minY

  // 估算面积 - 简单地将经纬度差转换为近似的平方公里
  // 在中国区域，1度经度约等于111公里×cos(纬度)，1度纬度约等于111公里
  const centerLat = (minY + maxY) / 2
  const cosLat = Math.cos(Cesium.Math.toRadians(centerLat))

  // 面积估算（平方公里）
  const areaKm2 = width * height * 111 * 111 * cosLat

  // 返回平方公里
  return areaKm2
}

// 隐藏省级区划和相关标签，创建省份下对应的市级区划
const createCityArea = async (province: string) => {
  // 1. 移除省级相关实体和数据源
  window.viewer.dataSources.removeAll()
  window.viewer.entities.removeAll()

  // 2. 加载市级geojson
  let geoJson
  try {
    geoJson = (await import(`../../data/geojson/province/${province}.json`)).default
  } catch (e) {
    const { message } = createDiscreteApi(['message'])
    message.error(`${province} 没有对应的市级geojson数据`)
    return
  }

  // 3. 创建市级数据源并渲染
  const dataSource = new Cesium.GeoJsonDataSource()
  // 加载GeoJSON数据
  const loadedDataSource = await dataSource.load(geoJson, {
    stroke: Cesium.Color.RED,
    fill: Cesium.Color.TRANSPARENT,
    strokeWidth: 5,
  })

  // 将数据源添加到viewer
  window.viewer.dataSources.add(loadedDataSource)
  // 获取所有实体
  const entities = dataSource.entities.values

  // 用于存储每个城市的最大面积和对应的实体信息
  const cityMaxAreaMap = new Map<
    string,
    {
      entity: any
      area: number
      positions: Cesium.Cartesian3[]
      centerX: number
      centerY: number
      centerZ: number
    }
  >()

  // 创建科技蓝渐变材质 - 使用标准的ColorMaterialProperty
  const terrainMaterial = new Cesium.ColorMaterialProperty(
    Cesium.Color.fromCssColorString('#337DFF').withAlpha(0.85),
  )

  // 创建底部边缘高亮材质
  const edgeHighlightMaterial = new Cesium.ColorMaterialProperty(
    Cesium.Color.fromCssColorString('#337DFF'),
  )

  // 为dataSource以外的区域添加暗色半透明遮罩
  const darkOverlay = new Cesium.Entity({
    rectangle: {
      coordinates: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90), // 全球范围
      material: new Cesium.ColorMaterialProperty(new Cesium.Color(0.05, 0.05, 0.1, 0.7)), // 暗色半透明
      height: 0,
      outline: false,
    },
  })
  window.viewer.entities.add(darkOverlay)

  // 为每个城市应用颜色，并收集面积信息
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]

    if (entity.polygon) {
      // 设置多边形填充为地形贴图
      entity.polygon.material = terrainMaterial

      // 设置多边形的侧面材质（增强立体感）- 注意：如果extrudedMaterial不被支持，可以直接使用material
      try {
        // @ts-ignore - 某些Cesium版本支持extrudedMaterial
        entity.polygon.extrudedMaterial = new Cesium.ColorMaterialProperty(
          Cesium.Color.fromCssColorString('#33d9b2').withAlpha(0.95),
        )
      } catch (e) {
        console.log('extrudedMaterial不被支持，使用标准material')
      }

      // 添加立体效果 - 设置挤出高度
      entity.polygon.extrudedHeight = new Cesium.ConstantProperty(100000) // 设置挤出高度为100km

      // 关闭所有轮廓
      entity.polygon.outline = new Cesium.ConstantProperty(false)

      // 设置多边形的阴影效果
      entity.polygon.shadows = new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED)

      // 创建一个顶面轮廓实体（同样的多边形但没有挤出）
      const outlineEntity = new Cesium.Entity({
        polygon: new Cesium.PolygonGraphics({
          hierarchy: entity.polygon.hierarchy,
          height: new Cesium.ConstantProperty(100000), // 与挤出高度相同
          material: Cesium.Color.TRANSPARENT,
          outline: true,
          outlineColor: Cesium.Color.fromCssColorString('#ffffff'), // 青色高亮边缘
          outlineWidth: 3,
        }),
      })
      window.viewer.entities.add(outlineEntity)

      // 使用Polyline绘制边界轮廓，解决Windows平台上outlineWidth不生效的问题
      // if (entity.polygon.hierarchy) {
      //   const hierarchy = entity.polygon.hierarchy.getValue()
      //   if (hierarchy && hierarchy.positions) {
      //     const outlinePolyline = new Cesium.Entity({
      //       polyline: new Cesium.PolylineGraphics({
      //         positions: hierarchy.positions,
      //         width: 2,
      //         material: Cesium.Color.fromCssColorString('#ffffff'),
      //         clampToGround: false,
      //         shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
      //       }),
      //       position: new Cesium.ConstantPositionProperty(new Cesium.Cartesian3(0, 100000, 0)),
      //     })
      //     window.viewer.entities.add(outlinePolyline)
      //   }
      // }

      // 额外添加一个顶部边缘发光线
      // if (entity.polygon.hierarchy) {
      //   const hierarchy = entity.polygon.hierarchy.getValue()
      //   if (hierarchy && hierarchy.positions) {
      //     const topEdgeEntity = new Cesium.Entity({
      //       polyline: new Cesium.PolylineGraphics({
      //         positions: hierarchy.positions,
      //         width: new Cesium.ConstantProperty(4),
      //         material: new Cesium.PolylineGlowMaterialProperty({
      //           glowPower: new Cesium.ConstantProperty(0.1),
      //           taperPower: new Cesium.ConstantProperty(0.2),
      //           color: Cesium.Color.fromCssColorString('#00FFFF'),
      //         }),
      //         // 注意：PolylineGraphics可能不支持height属性
      //         // @ts-ignore - 尝试设置高度，如果不支持会被忽略
      //         height: new Cesium.ConstantProperty(100000), // 与挤出高度相同
      //         shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
      //       }),
      //     })
      //     window.viewer.entities.add(topEdgeEntity)
      //   }
      // }

      // 创建底部边缘高亮实体
      // if (entity.polygon.hierarchy) {
      //   const hierarchy = entity.polygon.hierarchy.getValue()
      //   if (hierarchy && hierarchy.positions) {
      //     // 创建底部边缘高亮实体 - 使用预定义的edgeHighlightMaterial
      //     const bottomEdgeEntity = new Cesium.Entity({
      //       polyline: new Cesium.PolylineGraphics({
      //         positions: hierarchy.positions,
      //         width: new Cesium.ConstantProperty(5), // 增加线宽
      //         material: new Cesium.PolylineGlowMaterialProperty({
      //           glowPower: new Cesium.ConstantProperty(0.15), // 减小glowPower增强发光效果
      //           taperPower: new Cesium.ConstantProperty(0.3), // 减小taperPower增强发光效果
      //           color: Cesium.Color.fromCssColorString('#00FFFF'), // 移除透明度以增强可见性
      //         }),
      //         clampToGround: new Cesium.ConstantProperty(true),
      //         shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
      //       }),
      //     })

      //     // 额外创建一个底部边缘实体，使用edgeHighlightMaterial
      //     const bottomEdgeHighlightEntity = new Cesium.Entity({
      //       polyline: new Cesium.PolylineGraphics({
      //         positions: hierarchy.positions,
      //         width: new Cesium.ConstantProperty(8), // 稍细一些的线宽
      //         material: edgeHighlightMaterial,
      //         clampToGround: new Cesium.ConstantProperty(true),
      //         shadows: new Cesium.ConstantProperty(Cesium.ShadowMode.ENABLED),
      //       }),
      //     })
      //     window.viewer.entities.add(bottomEdgeHighlightEntity)
      //     window.viewer.entities.add(bottomEdgeEntity)
      //   }
      // }

      // 设置多边形高度为0，从地面开始挤出
      entity.polygon.height = new Cesium.ConstantProperty(0)

      // 计算多边形面积和中心点
      if (entity.properties && entity.properties.name && entity.polygon.hierarchy) {
        const cityName = entity.properties.name.getValue()
        entity.name = cityName

        const polyPositions = entity.polygon.hierarchy.getValue().positions
        if (polyPositions && polyPositions.length > 0) {
          // 使用 turf 计算几何中心点（质心）
          const center = calculatePolygonCenterWithTurf(polyPositions)
          const centerX = center.x
          const centerY = center.y
          const centerZ = center.z

          // 计算多边形面积
          const area = calculatePolygonArea(polyPositions)

          // 检查是否已有该城市的面积记录，如果没有或当前面积更大，则更新
          if (!cityMaxAreaMap.has(cityName) || area > cityMaxAreaMap.get(cityName)!.area) {
            cityMaxAreaMap.set(cityName, {
              entity,
              area,
              positions: polyPositions,
              centerX,
              centerY,
              centerZ,
            })
          }
        }
      }
    }
  }

  // 只为每个城市的最大面积区域创建标签
  cityMaxAreaMap.forEach((cityInfo, cityName) => {
    const { centerX, centerY, centerZ } = cityInfo

    // 创建一个单一的实体容器，包含城市名称和天线图标
    const containerEntity = window.viewer.entities.add({
      position: new Cesium.Cartesian3(centerX, centerY + 100000, centerZ), // 在多边形上方显示
      name: cityName, // 设置实体名称为城市名称
      // 城市名称标签
      label: new Cesium.LabelGraphics({
        text: new Cesium.ConstantProperty(cityName),
        font: new Cesium.ConstantProperty('18px bold 隶书'),
        fillColor: new Cesium.ConstantProperty(Cesium.Color.YELLOW),
        outlineColor: new Cesium.ConstantProperty(Cesium.Color.BLACK),
        outlineWidth: new Cesium.ConstantProperty(3),
        style: new Cesium.ConstantProperty(Cesium.LabelStyle.FILL_AND_OUTLINE),
        verticalOrigin: new Cesium.ConstantProperty(Cesium.VerticalOrigin.TOP),
        horizontalOrigin: new Cesium.ConstantProperty(Cesium.HorizontalOrigin.CENTER),
        pixelOffset: new Cesium.ConstantProperty(new Cesium.Cartesian2(0, -85)), // 减小向上偏移距离
        disableDepthTestDistance: new Cesium.ConstantProperty(Number.POSITIVE_INFINITY),
        eyeOffset: new Cesium.ConstantProperty(new Cesium.Cartesian3(0, 0, 0)),
        // scaleByDistance: new Cesium.ConstantProperty(
        //   new Cesium.NearFarScalar(1.5e2, 1.6, 1.5e7, 0.7),
        // ),
      }),
      // 天线图标
      billboard: new Cesium.BillboardGraphics({
        image: new Cesium.ConstantProperty(
          new URL('@/assets/images/program-coverage/antenna.png', import.meta.url).href,
        ),
        scale: new Cesium.ConstantProperty(0.7),
        horizontalOrigin: new Cesium.ConstantProperty(Cesium.HorizontalOrigin.CENTER),
        verticalOrigin: new Cesium.ConstantProperty(Cesium.VerticalOrigin.BOTTOM),
        pixelOffset: new Cesium.ConstantProperty(new Cesium.Cartesian2(0, 0)), // 减小向下偏移距离
        disableDepthTestDistance: new Cesium.ConstantProperty(Number.POSITIVE_INFINITY),
        eyeOffset: new Cesium.ConstantProperty(new Cesium.Cartesian3(0, 0, 0)),
        // scaleByDistance: new Cesium.ConstantProperty(
        //   new Cesium.NearFarScalar(1.5e2, 1.6, 1.5e7, 0.7),
        // ),
        // translucencyByDistance: new Cesium.ConstantProperty(
        //   new Cesium.NearFarScalar(1.5e2, 1.6, 1.5e7, 0.7),
        // ),
      }),
    })
    // 添加用户数据
    if (containerEntity) {
      // @ts-ignore
      containerEntity.userData = {
        type: 'cityAntenna',
      }
    }
  })

  // 增强阴影效果
  const scene = window.viewer.scene
  // scene.shadowMap.enabled = true
  // scene.shadowMap.darkness = 0.5
  // scene.shadowMap.softShadows = true
  // scene.shadowMap.size = 4096

  // 创建一个方向光源来增强阴影效果
  const sunPosition = Cesium.Cartesian3.fromDegrees(100, 30, 20000000) // 从东南方向照射

  // 计算光照方向 - 从太阳位置指向地球中心
  const toEarthCenter = new Cesium.Cartesian3()
  Cesium.Cartesian3.subtract(Cesium.Cartesian3.ZERO, sunPosition, toEarthCenter)
  const lightDirection = new Cesium.Cartesian3()
  Cesium.Cartesian3.normalize(toEarthCenter, lightDirection)

  // 设置场景光照
  scene.light = new Cesium.SunLight({
    color: Cesium.Color.WHITE,
    intensity: 2.0,
  })

  // 设置光照以增强立体效果
  scene.globe.enableLighting = true
  scene.globe.dynamicAtmosphereLighting = true
  scene.globe.dynamicAtmosphereLightingFromSun = true

  // 增强环境光和反射
  scene.globe.baseColor = Cesium.Color.fromCssColorString('#1B1D21')
  scene.globe.showGroundAtmosphere = true

  // 视角定位到中国
  window.viewer.flyTo(dataSource, {
    duration: 2.0,
    offset: new Cesium.HeadingPitchRange(Cesium.Math.toRadians(0), Cesium.Math.toRadians(-45), 3e6),
  })
}

const onPick = (movement: any) => {
  // 使用坐标转换工具修复缩放插件的坐标问题
  const transformedMovement = window.cesiumTransformTool?.transformCesiumEvent(movement) || movement

  // 获取点击位置的实体
  const pickedFeature = window.viewer.scene.pick(transformedMovement.position)
  if (Cesium.defined(pickedFeature) && pickedFeature.id && pickedFeature.id.billboard) {
    const userData = pickedFeature.id.userData
    if (userData) {
      if (userData.type === 'provinceAntenna') {
        return onClickProvinceAntenna(transformedMovement)
      } else if (userData.type === 'cityAntenna') {
        return onClickCityAntenna(transformedMovement)
      }
    }

    return
  }
  if (Cesium.defined(pickedFeature) && pickedFeature.id) {
    onPickProvince(transformedMovement)
  }
}
/**
 * 监听省份区域点击事件
 * @param movement 鼠标事件
 */
const onPickProvince = (movement: any) => {
  if (currentLevel.value !== 'province') return
  // 获取点击位置的实体
  const pickedFeature = window.viewer.scene.pick(movement.position)

  const entity = pickedFeature.id

  // 检查是否是省份实体
  if (entity.name) {
    const provinceName = entity.name
    console.log('点击省份实体:', provinceName)
    // 触发自定义事件，传递省份数据
    const event = new CustomEvent('provinceClick', {
      detail: {
        name: provinceName,
      },
    })
    window.dispatchEvent(event)
    createCityArea(provinceName)
    currentLevel.value = 'city'
  }
}

/**
 * 监听省份天线图标点击事件
 * @param movement 鼠标事件
 */
const onClickProvinceAntenna = (movement: any) => {
  // 获取点击位置的实体
  const pickedFeature = window.viewer.scene.pick(movement.position)
  const entity = pickedFeature.id

  // 检查是否是有效实体
  if (entity.name) {
    const provinceName = entity.name.toString()
    console.log('点击省份天线:', provinceName)

    // 使用转换后的坐标进行世界坐标转换
    const transformedPosition =
      window.cesiumTransformTool?.transformMousePosition(movement.position) || movement.position
    const screenPosition = new Cesium.Cartesian2(transformedPosition.x, transformedPosition.y)
    const scene = window.viewer.scene
    const ellipsoid = scene.globe.ellipsoid
    const worldPosition = scene.camera.pickEllipsoid(screenPosition, ellipsoid)

    // 触发自定义事件，传递省份数据
    const event = new CustomEvent('provinceAntennaClick', {
      detail: {
        name: provinceName,
        position: worldPosition,
      },
    })
    window.dispatchEvent(event)
  }
}

/**
 * 监听城市天线图标点击事件
 * @param movement 鼠标事件
 */
const onClickCityAntenna = (movement: any) => {
  // 获取点击位置的实体
  const pickedFeature = window.viewer.scene.pick(movement.position)
  const entity = pickedFeature.id

  // 检查是否是有效实体
  if (entity.name) {
    const name = entity.name.toString()
    console.log('点击城市天线:', name)

    // 使用转换后的坐标进行世界坐标转换
    const transformedPosition =
      window.cesiumTransformTool?.transformMousePosition(movement.position) || movement.position
    const screenPosition = new Cesium.Cartesian2(transformedPosition.x, transformedPosition.y)
    const scene = window.viewer.scene
    const ellipsoid = scene.globe.ellipsoid
    const worldPosition = scene.camera.pickEllipsoid(screenPosition, ellipsoid)

    // 触发自定义事件，传递城市数据
    const event = new CustomEvent('cityAntennaClick', {
      detail: {
        name: name,
        position: worldPosition,
      },
    })
    window.dispatchEvent(event)
  }
}

// 切换到省级视图
export const backProvince = () => {
  // 1. 移除省级相关实体和数据源
  window.viewer.dataSources.removeAll()
  window.viewer.entities.removeAll()
  createProvinceArea()
  currentLevel.value = 'province'
}

export const fuwei = () => {
  if (currentLevel.value === 'city') {
    window.viewer.flyTo(window.viewer.dataSources.get(0), {
      duration: 2.0,
      offset: new Cesium.HeadingPitchRange(
        Cesium.Math.toRadians(0),
        Cesium.Math.toRadians(-45),
        3e6,
      ),
    })
  } else {
    window.viewer.flyTo(window.viewer.dataSources.get(0), {
      duration: 2.5,
      offset: new Cesium.HeadingPitchRange(
        Cesium.Math.toRadians(0),
        Cesium.Math.toRadians(-65),
        1e7,
      ),
    })
  }
}

export const initScene = async () => {
  await createProvinceArea()
  // 添加点击事件监听
  window.viewer.screenSpaceEventHandler.setInputAction(
    onPick,
    Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
  )
}

export const clearScene = () => {
  const scene = window.viewer.scene
  // 移除点击事件监听
  window.viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)

  // 清除所有数据源和实体
  window.viewer.dataSources.removeAll()
  window.viewer.entities.removeAll()

  // 重置光照设置
  if (scene) {
    scene.globe.enableLighting = false
    scene.shadowMap.enabled = false
  }
  currentLevel.value = 'province'
}
