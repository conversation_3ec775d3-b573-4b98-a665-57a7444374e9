<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-07-20
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-21
-->
<template>
  <div class="space-y-7">
    <Container title="核心资产数据">
      <div class="h-[130px] pt-4 flex items-center gap-x-12">
        <div class="flex items-start">
          <img class="w-[148px] h-[110px] -ml-2" src="@/assets/images/dataCenterLayout/机架数量.png" alt="机架数量" />
          <div class="pt-4 space-y-1 -ml-4">
            <div class="flex items-center gap-x-2">
              <span class="text-gradual font-italic text-base">机架数量</span>
              <img src="@/assets/images/dataCenterLayout/up.png" alt="up" />
            </div>
            <div>
              <span class="font-['DINNextLTPro-Medium'] text-3xl text-[#32DCFB] font-500">35</span>
              <span class="text-xl text-[#32DCFB] font-500">组</span>
            </div>
          </div>
        </div>

        <div class="flex items-start">
          <img class="w-[148px] h-[110px] -ml-2" src="@/assets/images/dataCenterLayout/设备数量.png" alt="设备数量" />
          <div class="pt-4 space-y-1 -ml-4">
            <div class="flex items-center gap-x-2">
              <span class="text-gradual font-italic text-base">设备数量</span>
              <img src="@/assets/images/dataCenterLayout/down.png" alt="up" />
            </div>
            <div>
              <span class="font-['DINNextLTPro-Medium'] text-3xl text-[#32DCFB] font-500">132</span>
              <span class="text-xl text-[#32DCFB] font-500">台</span>
            </div>
          </div>
        </div>
      </div>
    </Container>

    <Container title="机房环境">
      <div class="h-[134px] px-2.5">
        <div class="h-11 flex items-center gap-x-8 border-b border-b-solid border-white/74">
          <div class="text-center w-30">机房</div>
          <div class="flex flex-1 items-center justify-center gap-x-1">
            <img class="size-5.5" src="@/assets/images/dataCenterLayout/温度.png" alt="温度" />
            <span>温度</span>
          </div>
          <div class="flex flex-1 items-center justify-center gap-x-1">
            <img class="size-5.5" src="@/assets/images/dataCenterLayout/湿度.png" alt="湿度" />
            <span>湿度</span>
          </div>
        </div>
        <div>
          <div class="h-10 flex items-center gap-x-8 border-b border-b-solid border-[#D8E3EE]/10">
            <div class="text-center w-30">信号机房</div>
            <div class="flex flex-1 items-center justify-center">
              <span>18</span>
            </div>
            <div class="flex flex-1 items-center justify-center">
              <span>36</span>
            </div>
          </div>
          <div class="h-10 flex items-center gap-x-8 border-b border-b-solid border-white/74">
            <div class="text-center w-30">UPS机房</div>
            <div class="flex flex-1 items-center justify-center">
              <span>18</span>
            </div>
            <div class="flex flex-1 items-center justify-center">
              <span>36</span>
            </div>
          </div>
        </div>
      </div>
    </Container>

    <Container title="机架用电数据">
      <div class="h-[205px]">
        <Echart :options="rackOptions" />
      </div>
    </Container>

    <Container title="设备故障统计">
      <div class="h-[205px] flex items-center pl-9">
        <div class="relative w-[190px] h-[159px] bg-cover bg-no-repeat bg-center" :style="{
          backgroundImage: `url(${getAssetsImage('设备故障统计-chart.jpg', 'dataCenterLayout')})`,
        }">
          <Echart :options="faultOptions" />

          <div
            class="gradient-border size-[89px] rounded-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center justify-center"
            style="
              background: linear-gradient(
                0deg,
                rgba(219, 235, 255, 0.3) 0%,
                rgba(219, 235, 255, 0) 100%
              );
              box-shadow: 0 0 0 1px rgba(249, 252, 255, 0.3);
            ">
            <img src="@/assets/images/dataCenterLayout/设备故障统计-1.png" class="w-[55px] h-[63px]" />
          </div>
        </div>
        <div class="ml-18 space-y-1.5">
          <div v-for="(item, index) in faultData" :key="index"
            class="flex items-center text-base font-['AlibabaPuHuiTi'] text-[#FCFCFC]/80">
            <div class="w-4.5 h-1.5 rounded-[3px]" :style="{ backgroundColor: item.itemStyle.color }"></div>
            <div class="ml-4 mr-20">{{ item.name }}</div>
            <div>{{ item.value }}</div>
          </div>
        </div>
      </div>
    </Container>

    <Container title="设备故障信息">
      <div class="h-[164px] px-2.5">
        <div
          class="h-9 flex items-center gap-x-2 border-b border-b-solid border-white/74 font-['AlibabaPuHuiTi'] text-base">
          <div class="w-12"></div>
          <div class="flex w-40 items-center justify-center">时间</div>
          <div class="flex flex-1 items-center justify-center">设备</div>
          <div class="flex flex-1 items-center justify-center">告警信息</div>
        </div>
        <div class="tbody font-['AlibabaPuHuiTi'] text-sm">
          <div class="h-10 flex items-center gap-x-2 border-b border-b-solid border-[#D8E3EE]/10">
            <div class="w-12 flex items-center justify-center">
              <img class="size-5" src="@/assets/images/dataCenterLayout/告警-1.png" alt="告警" />
            </div>
            <div class="flex w-40 items-center justify-center">2025-05-26 12:11:23</div>
            <div class="flex flex-1 items-center justify-center">调制器主1</div>
            <div class="flex flex-1 items-center justify-center text-[#F96168]">
              输入信号切换至备路
            </div>
          </div>

          <div class="h-10 flex items-center gap-x-2 border-b border-b-solid border-[#D8E3EE]/10">
            <div class="w-12 flex items-center justify-center">
              <img class="size-5" src="@/assets/images/dataCenterLayout/告警-2.png" alt="告警" />
            </div>
            <div class="flex w-40 items-center justify-center">2025-05-26 12:11:23</div>
            <div class="flex flex-1 items-center justify-center">调制器主1</div>
            <div class="flex flex-1 items-center justify-center text-[#F96168]">
              输入信号切换至备路
            </div>
          </div>

          <div class="h-10 flex items-center gap-x-2 border-b border-b-solid border-[#D8E3EE]/10">
            <div class="w-12 flex items-center justify-center">
              <img class="size-5" src="@/assets/images/dataCenterLayout/告警-3.png" alt="告警" />
            </div>
            <div class="flex w-40 items-center justify-center">2025-05-26 12:11:23</div>
            <div class="flex flex-1 items-center justify-center">调制器主1</div>
            <div class="flex flex-1 items-center justify-center text-[#F96168]">
              输入信号切换至备路
            </div>
          </div>
        </div>
      </div>
    </Container>
  </div>
  <Label />
  <UpsPowerPanel />
  <UpsPositions v-if="layoutStore.dataCenterRoom === 'UPS机房'" />
  <CjPowerPanel />
  <CjPositions v-if="layoutStore.dataCenterRoom === '信号机房'" />
  <Teleport to=".screen-wrapper">
    <Icon name="fuwei" class="fuwei" :style="{ left: layoutStore.showLeftPanel ? '640px' : '50px' }" title="复位"
      @click="fuwei" />
  </Teleport>
</template>

<script setup lang="ts">
import { getAssetsImage } from '@/utils/common'
import * as echarts from 'echarts'
import Echart from '@/components/Echart/Echart.vue'
import Container from '@/components/Container/Container.vue'
import type { EChartsOption } from 'echarts'
import Label from '@/views/overview/components/upsLabel.vue'
import UpsPowerPanel from './components/upsPowerPanel.vue'
import CjPowerPanel from './components/cjPowerPanel.vue'
import UpsPositions from './components/upsPositions.vue'
import CjPositions from './components/cjPositions.vue'
import { useLayoutStore } from '@/stores/layoutStore'
import { fuwei } from './index'
import Icon from '@/components/Icon/Icon.vue'

const layoutStore = useLayoutStore()

const faultData = [
  { value: 100, name: '高功放', itemStyle: { color: '#0066EF' } },
  { value: 150, name: '调制器', itemStyle: { color: '#11D0FC' } },
  { value: 214, name: '变频器', itemStyle: { color: '#9CCDFC' } },
  { value: 193, name: '天控器', itemStyle: { color: '#03B9FD' } },
  { value: 101, name: '信标机', itemStyle: { color: '#0066FF' } },
  { value: 165, name: '光端机', itemStyle: { color: '#3281EA' } },
]

// 机架用电数据
const rackOptions: EChartsOption = {
  grid: {
    top: 35,
    bottom: 10,
    left: 20,
    right: 20,
    containLabel: true,
  },
  legend: {
    show: true,
    left: 'center',
  },
  tooltip: {
    show: true,
    trigger: 'item',
  },
  xAxis: [
    {
      show: true,
      type: 'category',
      boundaryGap: true,
      data: ['A机架', 'B机架', 'C机架', 'D机架', 'E机架'],
      axisLine: {
        show: true,
      },
      axisLabel: {
        fontFamily: 'AlibabaPuHuiTi',
        fontWeight: 300,
        fontSize: 13,
        color: '#fff',
      },
    },
    {
      type: 'category',
      boundaryGap: true,
      data: ['A机架', 'B机架', 'C机架', 'D机架', 'E机架'],
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitArea: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
  ],
  yAxis: {
    axisLine: {
      show: false,
    },
    axisLabel: {
      show: true,
      fontFamily: 'AlibabaPuHuiTi',
      fontWeight: 300,
      fontSize: 14,
      color: '#fff',
    },
    splitLine: {
      lineStyle: {
        color: '#B2C2D3',
        opacity: 0.2,
      },
    },
  },
  series: [
    // 背景柱子
    {
      name: '',
      type: 'bar',
      xAxisIndex: 1,
      barWidth: 12,
      barGap: '30%',
      itemStyle: {
        color: 'rgba(187,230,245,0.22)',
        opacity: 0.9,
      },
      data: [100, 100, 100, 100, 100],
      zlevel: 1,
      silent: true,
      animation: false,
      legendHoverLink: false,
      tooltip: {
        show: false,
      },
    },
    {
      name: '',
      type: 'bar',
      xAxisIndex: 1,
      barWidth: 12,
      itemStyle: {
        color: 'rgba(187,230,245,0.22)',
        opacity: 0.9,
      },
      data: [100, 100, 100, 100, 100],
      zlevel: 1,
      silent: true,
      animation: false,
      legendHoverLink: false,
      tooltip: {
        show: false,
      },
    },
    {
      name: '',
      type: 'bar',
      xAxisIndex: 1,
      barWidth: 12,
      itemStyle: {
        color: 'rgba(187,230,245,0.22)',
        opacity: 0.9,
      },
      data: [100, 100, 100, 100, 100],
      zlevel: 1,
      silent: true,
      animation: false,
      legendHoverLink: false,
      tooltip: {
        show: false,
      },
    },
    // 电压数据
    {
      name: '电压',
      type: 'bar',
      barWidth: 12,
      barGap: '30%',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#B2E5EE',
          },
          {
            offset: 0.15,
            color: '#1DA7C0',
          },
          {
            offset: 1,
            color: '#1DA7C0',
          },
        ]),
      },
      data: [45, 10, 42, 25, 68],
      zlevel: 2,
    },
    // 电流数据
    {
      name: '电流',
      type: 'bar',
      barWidth: 12,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#F1F0C5',
          },
          {
            offset: 0.15,
            color: '#FFD631',
          },
          {
            offset: 1,
            color: '#FFD631',
          },
        ]),
      },
      data: [58, 8, 43, 23, 66],
      zlevel: 2,
    },
    // 功率数据
    {
      name: '功率',
      type: 'bar',
      barWidth: 12,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#D8F7DD',
          },
          {
            offset: 0.15,
            color: '#99F17A',
          },
          {
            offset: 1,
            color: '#99F17A',
          },
        ]),
      },
      data: [46, 9, 41, 24, 67],
      zlevel: 2,
    },
  ],
}

// 设备故障统计
const faultOptions: EChartsOption = {
  tooltip: {
    trigger: 'item',
  },
  legend: {
    show: false,
  },
  xAxis: {
    show: false,
  },
  yAxis: {
    show: false,
  },
  series: [
    {
      name: '设备故障统计',
      type: 'pie',
      center: ['50%', '50%'],
      radius: ['63%', '78%'],
      padAngle: 5,
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
      },
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: faultData,
    },
  ],
}
</script>

<style scoped lang="scss">
.text-gradual {
  @include text-gradual(#acddff, #fff);
}

.tbody>div:nth-child(odd) {
  background: linear-gradient(90deg, rgba(27, 130, 183, 0.22) 0%, rgba(35, 104, 162, 0) 100%);
}

.tbody>div:nth-child(even) {
  background: linear-gradient(90deg, rgba(35, 104, 162, 0.12) 0%, rgba(35, 104, 162, 0) 100%);
}

.fuwei {
  @apply fixed left-630px top-240px flex justify-center items-center rounded-full text-36px text-white bg-#1ea2ff88 w-54px h-54px z-110 cursor-pointer transition-all duration-300;

  &:hover {
    @apply bg-#1ea2ff;
  }
}
</style>
