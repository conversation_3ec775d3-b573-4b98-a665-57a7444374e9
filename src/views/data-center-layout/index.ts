/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-02
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-21
 */
import { threeJSEvents } from '@/three/evens'
import { viewer } from '@/three/viewer'
import { until } from '@/utils'
import { activeObject, clickPosition } from '@/views/overview/data'
import type { Mesh, Vector3 } from 'three'
import highlightManager from '@/three/highLight'
import { navigate } from '@/three/navigate'
import { useLayoutStore } from '@/stores/layoutStore'

let clickPoint: Vector3 | null = null
// 场景坐标转屏幕坐标
const transformPoint = () => {
  if (!clickPoint) {
    clickPosition.value = null
    return
  }
  const camera = viewer.getCamera()
  const renderer = viewer.getRenderer()
  const canvas = renderer.domElement

  // 复制一份，避免污染原始点
  const ndc = clickPoint.clone().project(camera)
  // NDC [-1,1] 转换为屏幕像素
  const x = ((ndc.x + 1) / 2) * canvas.clientWidth
  const y = ((-ndc.y + 1) / 2) * canvas.clientHeight
  clickPosition.value = { x, y }
}

// 清除激活的对象
export const clearLastActive = () => {
  if (activeObject.value) {
    highlightManager.clearObjectHighlight(activeObject.value)
    clickPosition.value = null
    threeJSEvents.unsubscribe('camera:change', transformPoint)
  }
}

// 设置激活对象
const setActiveObject = (object: Mesh, point: Vector3) => {
  clearLastActive()
  activeObject.value = object
  highlightManager.highlightObject(object)
  clickPoint = point
  transformPoint()
  threeJSEvents.subscribe('camera:change', transformPoint)
}

// 对象点击事件
const onObjectClick = (e: any) => {
  console.log('object:dblclick', e)
  const { object } = e
  if (object.name === activeObject.value?.name) {
    return
  }
  if (object.name.includes('UPS') || object.name.includes('PDG')) {
    setActiveObject(object, e.point)
  }
}

export const flyToSignal = () => {
  navigate.flyToWithRotation(
    [-8.541084895006183, 5.445428444379073, -15.224416276954889],
    [
      -0.310097337233988,

      -0.980035629053875,

      -0.26009815513592305,
    ],
  )
}

export const flyToUps = () => {
  navigate.flyToWithRotation(
    [-16.391840017173678, 5.268842808746415, -20.184239368237684],
    [-0.20506031966177637, 0.583205488601779, 0.11404018936724511],
  )
}

export const fuwei = () => {
  const layoutStore = useLayoutStore()
  if (layoutStore.dataCenterRoom === '信号机房') {
    flyToSignal()
  } else if (layoutStore.dataCenterRoom === 'UPS机房') {
    flyToUps()
  }
}

export const initScene = async () => {
  until(() => viewer?.isInited).then(() => {
    threeJSEvents.subscribe('object:dblclick', onObjectClick)
    // const rain = new Rain()
    // rain.init()
    // rain.start()
  })
}

export const clearScene = () => {
  threeJSEvents.unsubscribe('object:dblclick', onObjectClick)
  threeJSEvents.unsubscribe('camera:change', transformPoint)
  clearLastActive()
}
