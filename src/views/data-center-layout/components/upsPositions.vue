<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-08-09
 * @LastEditors: '余承'
 * @LastEditTime: 2025-08-21
-->
<template>
  <Teleport to=".screen-wrapper">
    <div class="positions">
      <view :class="['item']" :title="item.name" v-for="item in positions" :key="item.name" @click="handleClick(item)">
        <span>{{ item.title }}</span>
      </view>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { viewer } from '@/three/viewer'
import { navigate } from '@/three/navigate'
import { Box3, Vector3, type Mesh } from 'three'
import { clearLastActive, setActiveObject } from '@/views/overview'
import { useLayoutStore } from '@/stores/layoutStore'

const positions = ref([
  {
    name: 'GDYQ-UPSS-UPS-0001',
    title: '机柜1',
  },
  {
    name: "GDYQ-UPSS-UPS-0002",
    title: '机柜2',
  },
  {
    name: "GDYQ-UPSS-SRSCPDG-0001_1",
    title: '机柜3',
  },
  {
    name: "GDYQ-UPSS-UPS-0003",
    title: '机柜4',
  },
  {
    name: "GDYQ-UPSS-UPS-0004",
    title: '机柜5',
  },
  {
    name: "GDYQ-UPSS-UPS-0005",
    title: '机柜6',
  },
  {
    name: "GDYQ-UPSS-UPS-0006",
    title: '机柜7',
  },
])

const handleClick = (item: (typeof positions.value)[number]) => {
  const object = viewer.scene.getObjectByName(item.name)
  if (!object) {
    window.$message.error('未找到模型')
    return
  }
  // 计算模型包围盒
  const boundingBox = new Box3().setFromObject(object)

  const center = new Vector3()
  boundingBox.getCenter(center)

  // 计算包围盒尺寸和适当的相机距离
  const size = new Vector3()
  boundingBox.getSize(size)
  const maxDim = Math.max(size.x, size.y, size.z)
  const distance = maxDim * 0.88 // 距离是最大尺寸的2倍，可以根据需要调整

  // 计算相机位置：从中心点向上和向后偏移
  const direction = new Vector3(7, 0, 10).normalize()
  const position = center.clone().add(direction.multiplyScalar(distance))

  // 飞行到计算出的位置，并看向模型中心
  navigate.flyTo(position, center.clone().add(new Vector3(0, 0.5, 0).normalize().multiplyScalar(0.5)))
  //获取该模型中心没位置
  const wordPos = boundingBox.getCenter(new Vector3())

  clearLastActive()
  setActiveObject(object as Mesh, wordPos)
}
const layoutStore = useLayoutStore()

const right = computed(() => {
  return layoutStore.showRightPanel ? '666px' : '100px'
})
</script>

<style scoped lang="scss">
.positions {
  @apply fixed top-300px flex flex-col gap-16px z-100;
  right: v-bind('right');
  transition: right 0.5s ease;

  .item {
    @apply flex w-100px h-36px flex justify-center items-center rounded-6px cursor-pointer text-16px;
    background-color: rgba(49, 143, 229, 0.5);
    border: 1px solid rgba(27, 150, 255, 1);

    &:hover {
      background-color: rgba(49, 143, 229, 0.9);
    }
  }

  .active {
    background-color: rgba(49, 143, 229, 0.9);
  }
}
</style>
