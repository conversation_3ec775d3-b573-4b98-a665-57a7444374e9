<template></template>

<script lang="ts" setup>
import { viewer } from '@/three/viewer'
import * as THREE from 'three'
import { CSS3<PERSON>enderer, CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { until } from '@/utils'
import { onMounted, onUnmounted, ref } from 'vue'

const antennas = ref([
  {
    id: 0,
    name: 'GDYQ-CSJF-PDG-0001_4',
    voltage: 0,
    ampere: 0,
    power: 50,
    position: [
      -9.33295648401178,

      5.8955924247445942,

      -19.451125505020286,
    ], // 左侧标签
  },
  {
    id: 1,
    name: 'GDYQ-CSJF-PDG-0002_4',
    voltage: 0,
    power: 50,
    position: [
      -8.935915771787407,

      5.8955924247445942, -18.84357322507033,
    ], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 2,
    name: 'GDYQ-CSJF-PDG-0003_4',
    voltage: 0,
    power: 10,
    position: [
      -8.517558735467482, 5.8955924247445942,

      -18.197932307726877,
    ], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 3,
    name: 'GDYQ-CSJF-PDG-0004_4',
    voltage: 0,
    power: 60,
    position: [-8.18795200184039, 5.8955924247445942, -17.69572836246347], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 4,
    name: 'GDYQ-CSJF-PDG-0005_4',
    voltage: 0,
    power: 20,
    position: [-7.85288693216981, 5.8955924247445942, -17.19876508523636], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 5,
    name: 'GDYQ-CSJF-PDG-0006_4',
    voltage: 0,
    power: 20,
    position: [-7.537148697231461, 5.8955924247445942, -16.69207026778924], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 6,
    name: 'GDYQ-CSJF-PDG-0007_4',
    voltage: 0,
    power: 20,
    position: [-7.210261058152289, 5.8955924247445942, -16.187543590511424], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 7,
    name: 'GDYQ-CSJF-PDG-0008_4',
    voltage: 0,
    power: 20,
    position: [-6.887831162879317, 5.8955924247445942, -15.684952033966805], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 8,
    name: 'GDYQ-CSJF-PDG-0009_4',
    voltage: 0,
    power: 20,
    position: [-6.555947742918397, 5.8955924247445942, -15.173420318334099], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 9,
    name: 'GDYQ-CSJF-PDG-0010_4',
    voltage: 0,
    power: 20,
    position: [-6.228392951659338, 5.8955924247445942, -14.671401775614655], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 10,
    name: 'GDYQ-CSJF-PDG-0011_4',
    voltage: 0,
    power: 20,
    position: [-5.905068321212638, 5.8955924247445942, -14.170054703344391], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 11,
    name: 'GDYQ-CSJF-PDG-0012_1',
    voltage: 0,
    power: 20,
    position: [-5.574910400506679, 5.8955924247445942, -13.660683563999271], // 右侧标签，与左侧标签保持6个单位距离
  },
  {
    id: 12,
    name: 'GDYQ-CSJF-PDG-0013_1',
    voltage: 0,
    power: 20,
    position: [-5.262858303177216, 5.8955924247445942, -13.180258317358867], // 右侧标签，与左侧标签保持6个单位距离
  },
])

// 存储创建的CSS3D标签引用
const antennaLabels = ref<Map<number, CSS3DObject>>(new Map())

// 创建CSS3D渲染器
let css3dRenderer: CSS3DRenderer

// 添加相机位置检查函数
const isCameraInBoundingBox = () => {
  try {
    const targetObject = viewer.scene.getObjectByName('GDYQ-CSJF-ZT-0001')
    if (!targetObject) {
      console.warn('未找到目标对象: GDYQ-CSJF-ZT-0001')
      return false
    }

    // 计算对象的包围盒
    const boundingBox = new THREE.Box3().setFromObject(targetObject)

    // 获取相机位置
    const cameraPosition = viewer.camera.position

    // 检查相机是否在包围盒内
    return boundingBox.containsPoint(cameraPosition)
  } catch (error) {
    console.error('检查相机位置时出错:', error)
    return false
  }
}

// 显示/隐藏标签函数
const toggleLabelsVisibility = (visible: boolean) => {
  antennaLabels.value.forEach((labelObject) => {
    if (labelObject.element) {
      labelObject.element.style.display = visible ? 'block' : 'none'
    }
  })
}

// 创建天线信息面板的HTML元素
const createAntennaPanelHTML = (antenna: any) => {
  const container = document.createElement('div')
  container.classList.add('antenna-panel')
  // 根据功率值确定颜色主题
  let themeName: string

  if (antenna.power <= 2500) {
    // 绿色主题 - 低功率
    themeName = 'green'
  } else if (antenna.power <= 3500) {
    themeName = 'yellow'
  } else {
    themeName = 'red'
  }

  // 设置主题属性
  container.setAttribute('data-theme', themeName)

  // 创建内容区域
  const content = document.createElement('div')
  content.style.cssText = `
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    z-index: 3;
  `

  // 创建信息行
  const createInfoRow = (label: string, value: string, unit: string, className: string) => {
    const row = document.createElement('div')
    row.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      transition: all 0.15s ease-out;
    `

    const labelSpan = document.createElement('span')
    labelSpan.style.cssText = `
      color: #FFFFFF;
      font-size: 13px;
      font-weight: 500;
      flex: 1;
    `
    labelSpan.textContent = label
    row.appendChild(labelSpan)

    const valueSpan = document.createElement('span')
    valueSpan.className = className
    valueSpan.style.cssText = `
      color: #FFFFFF;
      font-size: 13px;
      font-weight: 600;
      margin-right: 4px;
    `
    valueSpan.textContent = value
    row.appendChild(valueSpan)

    const unitSpan = document.createElement('span')
    unitSpan.style.cssText = `
      color: #FFFFFF;
      font-size: 12px;
      font-weight: 400;
      opacity: 0.9;
    `
    unitSpan.textContent = unit
    row.appendChild(unitSpan)

    // 添加悬停效果
    row.addEventListener('mouseenter', () => {
      row.style.background = 'rgba(255, 255, 255, 0.1)'
      row.style.borderRadius = '4px'
      row.style.padding = '4px 8px'
      row.style.margin = '0 -8px'
    })

    row.addEventListener('mouseleave', () => {
      row.style.background = 'transparent'
      row.style.borderRadius = '0'
      row.style.padding = '4px 0'
      row.style.margin = '0'
    })

    return row
  }

  // 添加各项信息 - 根据图片显示电压、电流、功率
  content.appendChild(createInfoRow('电压', `${antenna.voltage || 220}`, 'V', 'voltage-text'))
  content.appendChild(createInfoRow('电流', `${antenna.ampere || 10}`, 'A', 'ampere-text'))
  content.appendChild(createInfoRow('功率', `${antenna.power || 3000}`, 'W', 'power-text'))

  container.appendChild(content)

  // 创建四个角的直角边框装饰
  const createCornerBorder = (position: string) => {
    const corner = document.createElement('div')
    corner.className = `corner-border corner-${position}`
    return corner
  }

  // 添加四个角的边框
  container.appendChild(createCornerBorder('top-left'))
  container.appendChild(createCornerBorder('top-right'))
  container.appendChild(createCornerBorder('bottom-left'))
  container.appendChild(createCornerBorder('bottom-right'))

  // 创建向下延伸的连接线
  const connectingLine = document.createElement('div')
  connectingLine.className = 'connecting-line'
  container.appendChild(connectingLine)

  // 创建连接线末端的小圆点
  const endCircle = document.createElement('div')
  endCircle.className = 'end-circle'
  container.appendChild(endCircle)

  // 添加点击事件
  container.addEventListener('click', () => {
    // 这里可以添加点击后的交互逻辑
  })

  return container
}

// 更新天线标签内容
const updateAntennaLabel = (antenna: any) => {
  const labelObject = antennaLabels.value.get(antenna.id)
  if (labelObject && labelObject.element) {
    // 获取现有的HTML元素
    const existingElement = labelObject.element
    let themeName = ''
    if (antenna.power <= 2500) {
      themeName = 'green'
    } else if (antenna.power <= 3500) {
      themeName = 'yellow'
    } else {
      themeName = 'red'
    }
    existingElement.setAttribute('data-theme', themeName)
    // 更新电压
    const voltageText = existingElement.querySelector('.voltage-text')
    if (voltageText) {
      voltageText.textContent = `${antenna.voltage || 220}`
    }

    // 更新电流
    const ampereText = existingElement.querySelector('.ampere-text')
    if (ampereText) {
      ampereText.textContent = `${antenna.ampere || 10}`
    }

    // 更新功率
    const powerText = existingElement.querySelector('.power-text')
    if (powerText) {
      powerText.textContent = `${antenna.power || 3000}`
    }
  }
}

// 初始化CSS3D渲染器
const initCSS3DRenderer = () => {
  css3dRenderer = new CSS3DRenderer()
  css3dRenderer.setSize(2560, 1440)
  css3dRenderer.domElement.style.position = 'absolute'
  css3dRenderer.domElement.style.top = '0'
  css3dRenderer.domElement.style.left = '0'
  css3dRenderer.domElement.style.pointerEvents = 'none'
  css3dRenderer.domElement.style.zIndex = '100' // 确保在最上层

  // 将CSS3D渲染器添加到页面
  document.querySelector('.layout')?.appendChild(css3dRenderer.domElement)
}

// 创建CSS3D场景
const css3dScene = new THREE.Scene()

const init3dPanel = () => {
  until(() => viewer?.isInited).then(() => {
    // 初始化CSS3D渲染器
    initCSS3DRenderer()

    // 为每个antenna在场景中指定位置创建CSS3D标签
    antennas.value.forEach((antenna) => {
      try {
        // 创建HTML元素
        const htmlElement = createAntennaPanelHTML(antenna)

        // 创建CSS3D对象
        const labelObject = new CSS3DObject(htmlElement)

        // 设置标签位置 - 直接使用数据中的实际坐标
        labelObject.position.set(antenna.position[0], antenna.position[1], antenna.position[2])

        // 设置固定的缩放值
        labelObject.scale.setScalar(0.003)

        // 调整标签朝向 - 使用rotation来调整，这样不会影响位置
        labelObject.rotation.y = -1 // 调整X轴旋转来改变朝向
        // 或者使用 lookAt 让标签始终面向相机
        // labelObject.lookAt(viewer.camera.position);

        // 存储天线信息到标签中
        labelObject.userData.antennaName = antenna.name
        labelObject.userData.antennaId = antenna.id

        // 添加到CSS3D场景
        css3dScene.add(labelObject)

        // 存储标签引用
        antennaLabels.value.set(antenna.id, labelObject)
      } catch (error) {
        console.error(`创建CSS3D天线标签失败: ${antenna.name}`, error)
      }
    })

    // 添加渲染循环，让CSS3D标签保持固定
    const updateCSS3DLabels = () => {
      // 检查相机是否在目标对象的包围盒内
      const shouldShowLabels = isCameraInBoundingBox()

      // 根据相机位置显示/隐藏标签
      toggleLabelsVisibility(shouldShowLabels)

      // 只有在应该显示标签时才渲染CSS3D场景
      if (shouldShowLabels) {
        css3dRenderer.render(css3dScene, viewer.camera)
      }
    }

    // 将更新函数添加到渲染循环
    viewer.addRenderCallback(updateCSS3DLabels)
  })
}

// 更新所有天线标签
const updateAllAntennaLabels = () => {
  antennas.value.forEach((antenna) => {
    updateAntennaLabel(antenna)
  })
}

// 模拟数据更新（用于演示）
const simulateDataUpdate = () => {
  setInterval(() => {
    antennas.value.forEach((antenna) => {
      // 随机更新电压、电流、功率数据
      antenna.voltage = Math.floor(Math.random() * 40) + 200 // 200-240V
      antenna.ampere = Math.floor(Math.random() * 20) + 5 // 5-25A
      antenna.power = Math.floor(Math.random() * 2000) + 2000 // 2000-4000W
    })

    // 更新标签内容
    updateAllAntennaLabels()
  }, 5000) // 每5秒更新一次
}

// 处理窗口大小变化
const handleResize = () => {
  if (css3dRenderer) {
    css3dRenderer.setSize(window.innerWidth, window.innerHeight)
  }
}

onMounted(() => {
  init3dPanel()
  // 启动数据更新模拟（可选）
  simulateDataUpdate()

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 清除所有antenna的CSS3D标签
  try {
    // 移除所有天线标签
    antennaLabels.value.forEach((labelObject, id) => {
      // 从CSS3D场景中移除
      css3dScene.remove(labelObject)

      // 清理HTML元素
      if (labelObject.element && labelObject.element.parentNode) {
        labelObject.element.parentNode.removeChild(labelObject.element)
      }
    })

    // 清空引用
    antennaLabels.value.clear()

    // 清理CSS3D渲染器
    if (css3dRenderer && css3dRenderer.domElement.parentNode) {
      css3dRenderer.domElement.parentNode.removeChild(css3dRenderer.domElement)
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize)
  } catch (error) {
    console.error('清理CSS3D天线标签时出错:', error)
  }
})
</script>

<style lang="scss">
.antenna-panel {
  /* 确保标签在3D场景中正确显示 */
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* 悬停效果增强 - 使用CSS变量支持动态主题 */
// .antenna-panel:hover {
//   transform: translateY(-2px);
//   box-shadow:
//     0 6px 20px rgba(255, 255, 255, 0.5),
//     0 4px 12px rgba(255, 255, 255, 0.4);
// }

// /* 连接线悬停效果 */
// .antenna-panel:hover .connecting-line {
//   background: #ffffff !important;
//   box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
// }

// .antenna-panel:hover .end-circle {
//   background: #ffffff !important;
//   box-shadow: 0 0 12px rgba(255, 255, 255, 0.9);
// }

// /* 角边框悬停效果 */
// .antenna-panel:hover .corner-border {
//   border-color: #ffffff !important;
//   box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
// }

// /* 角边框发光效果 */
// .corner-border {
//   transition: all 0.2s ease-out;
// }

// .corner-border:hover {
//   border-color: #ffffff !important;
//   box-shadow: 0 0 12px rgba(255, 255, 255, 0.9);
// }

/* 绿色主题特殊样式 */
.antenna-panel[data-theme='green'] {
  --theme-primary: #00ff00;
  --theme-secondary: #32cd32;
  --theme-border: #00ff00;
  --theme-shadow: rgba(0, 255, 0, 0.4);
}

/* 黄色主题特殊样式 */
.antenna-panel[data-theme='yellow'] {
  --theme-primary: #ffd700;
  --theme-secondary: #ffa500;
  --theme-border: #ffff00;
  --theme-shadow: rgba(255, 215, 0, 0.4);
}

/* 红色主题特殊样式 */
.antenna-panel[data-theme='red'] {
  --theme-primary: #ff4500;
  --theme-secondary: #dc143c;
  --theme-border: #ff0000;
  --theme-shadow: rgba(255, 69, 0, 0.4);
}

.antenna-panel {
  width: 180px;
  height: max-content;
  background: linear-gradient(135deg,
      color-mix(in srgb, var(--theme-primary) 80%, transparent) 0%,
      var(--theme-secondary) 50%,
      color-mix(in srgb, var(--theme-primary) 80%, transparent) 100%);
  border: 1px solid var(--theme-border);
  box-shadow:
    0 4px 15px var(--theme-shadow),
    0 2px 8px var(--theme-shadow);
  font-family: 'Inter', 'Source Han Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #ffffff;
  overflow: visible;
  position: relative;
  transition: all 0.2s ease-out;
  cursor: pointer;
  user-select: none;
  z-index: 1001;

  .corner-top-left,
  .corner-top-right,
  .corner-bottom-left,
  .corner-bottom-right {
    position: absolute;
    width: 12px;
    height: 12px;
    z-index: 4;
  }

  .corner-top-left {
    top: -2px;
    left: -2px;
    border-top: 2px solid var(--theme-border);
    border-left: 2px solid var(--theme-border);
  }

  .corner-top-right {
    top: -2px;
    right: -2px;
    border-top: 2px solid var(--theme-border);
    border-right: 2px solid var(--theme-border);
  }

  .corner-bottom-left {
    bottom: -2px;
    left: -2px;
    border-bottom: 2px solid var(--theme-border);
    border-left: 2px solid var(--theme-border);
  }

  .corner-bottom-right {
    bottom: -2px;
    right: -2px;
    border-bottom: 2px solid var(--theme-border);
    border-right: 2px solid var(--theme-border);
  }

  .connecting-line {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 25px;
    background: var(--theme-border);
    z-index: 2;
  }

  .end-circle {
    position: absolute;
    bottom: -32px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background: var(--theme-border);
    border-radius: 50%;
    box-shadow: 0 0 8px var(--theme-shadow);
    z-index: 2;
  }
}
</style>
